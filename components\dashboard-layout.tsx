"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { NavLink } from "@/components/ui/nav-link"
import { LoadingButton } from "@/components/ui/loading-button"
import DashboardHeader from "@/components/dashboard-header"
import { usePermissions } from "@/hooks/usePermissions"
import { Permission } from "@/lib/permissions"
import { logoutAndRedirect } from "@/lib/auth"
import {
  LucideBarChart2,
  LucideCalendarClock,
  LucideClipboardList,
  LucideCoins,
  LucideHeart,
  LucideHome,
  LucideLogOut,
  Plus,
  LucideSettings,
  LucideShoppingCart,
  LucideUsers,
} from "lucide-react"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isLogoutLoading, setIsLogoutLoading] = useState(false)
  const { hasPermission } = usePermissions()

  const handleLogout = async () => {
    setIsLogoutLoading(true)

    try {
      await logoutAndRedirect()
    } catch (error) {
      console.error('Logout error:', error)
      // Still redirect to logout page even if there's an error
      window.location.href = '/logout'
    } finally {
      setIsLogoutLoading(false)
    }
  }

  const menuItems = [
    {
      href: "/dashboard",
      icon: LucideHome,
      label: "Overview",
      iconColor: "text-green-600",
      permission: null // Dashboard is accessible to all authenticated users
    },
    {
      href: "/register",
      icon: Plus,
      label: "Register",
      iconColor: "text-blue-600",
      permission: "view_goats" as Permission // Using same permission as animal management
    },
    {
      href: "/goats",
      icon: LucideUsers,
      label: "Goats",
      iconColor: "text-emerald-600",
      permission: "view_goats" as Permission
    },
    {
      href: "/sheep",
      icon: LucideUsers,
      label: "Sheep",
      iconColor: "text-blue-600",
      permission: "view_goats" as Permission // Using same permission for now
    },
    {
      href: "/cattle",
      icon: LucideUsers,
      label: "Cattle",
      iconColor: "text-amber-600",
      permission: "view_goats" as Permission // Using same permission for now
    },
    {
      href: "/pigs",
      icon: LucideUsers,
      label: "Pigs",
      iconColor: "text-pink-600",
      permission: "view_goats" as Permission // Using same permission for now
    },
    {
      href: "/health",
      icon: LucideHeart,
      label: "Health Records",
      iconColor: "text-green-600",
      permission: "add_health_records" as Permission
    },
    {
      href: "/breeding",
      icon: LucideCalendarClock,
      label: "Breeding",
      iconColor: "text-teal-600",
      permission: "manage_breeding" as Permission
    },
    {
      href: "/feeding",
      icon: LucideClipboardList,
      label: "Feeding",
      iconColor: "text-emerald-600",
      permission: "manage_feeding" as Permission
    },
    {
      href: "/finance",
      icon: LucideCoins,
      label: "Finance",
      iconColor: "text-green-600",
      permission: "manage_finances" as Permission
    },
    {
      href: "/inventory",
      icon: LucideShoppingCart,
      label: "Inventory",
      iconColor: "text-teal-600",
      permission: "manage_inventory" as Permission
    },
    {
      href: "/reports",
      icon: LucideBarChart2,
      label: "Reports",
      iconColor: "text-emerald-600",
      permission: "view_reports" as Permission
    },
  ]

  // Filter menu items based on user permissions
  const visibleMenuItems = menuItems.filter(item =>
    item.permission === null || hasPermission(item.permission)
  )

  return (
    <div className="flex min-h-screen flex-col bg-gray-50 dark:bg-gray-900">
      <DashboardHeader />
      <div className="flex flex-1">
        <aside className="hidden w-64 border-r bg-white dark:bg-gray-800 md:block">
          <div className="flex h-full flex-col gap-2 p-4">
            <div className="py-2">
              <h2 className="px-4 text-lg font-semibold tracking-tight text-gradient-primary">Dashboard</h2>
            </div>
            <div className="flex-1 space-y-1">
              {visibleMenuItems.map((item) => (
                <NavLink
                  key={item.href}
                  href={item.href}
                  className="flex items-center gap-3 rounded-lg px-4 py-2 text-sm font-medium transition-colors hover:bg-emerald-50 hover:text-emerald-700 w-full"
                  activeClassName="bg-gradient-to-r from-emerald-100 to-teal-50 text-emerald-700 font-medium border-l-2 border-l-emerald-500"
                >
                  <item.icon className={`h-5 w-5 ${item.iconColor}`} />
                  {item.label}
                </NavLink>
              ))}
            </div>
            <div className="mt-auto space-y-1 pt-4 border-t">
              {hasPermission("system_settings") && (
                <NavLink
                  href="/settings"
                  className="flex items-center gap-3 rounded-lg px-4 py-2 text-sm font-medium transition-colors hover:bg-teal-50 hover:text-teal-700 w-full"
                  activeClassName="bg-gradient-to-r from-teal-100 to-teal-50 text-teal-700 font-medium border-l-2 border-l-teal-500"
                >
                  <LucideSettings className="h-5 w-5 text-teal-600" />
                  Settings
                </NavLink>
              )}
              <LoadingButton
                variant="ghost"
                className="flex items-center gap-3 rounded-lg px-4 py-2 text-sm font-medium w-full justify-start transition-colors hover:bg-red-50 hover:text-red-700"
                isLoading={isLogoutLoading}
                onClick={handleLogout}
              >
                <LucideLogOut className="h-5 w-5 text-red-500" />
                Logout
              </LoadingButton>
            </div>
          </div>
        </aside>
        <main className="flex-1 overflow-auto p-4 md:p-6 lg:p-8">
          <div className="mx-auto max-w-6xl">{children}</div>
        </main>
      </div>
    </div>
  )
}

