import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');

    console.log('GET /api/cattle - Gender filter:', gender);

    // Query to get all cattle with calculated age
    let query = `
      SELECT
        id,
        tag_number,
        name,
        breed,
        gender,
        status,
        birth_date,
        weight,
        color,
        markings,
        notes,
        CASE
          WHEN birth_date IS NOT NULL THEN
            CONCAT(
              FLOOR(DATEDIFF(CURDATE(), birth_date) / 365),
              ' years, ',
              FLOOR((DATEDIFF(CURDATE(), birth_date) % 365) / 30),
              ' months'
            )
          ELSE 'Unknown'
        END as age
      FROM animals
      WHERE animal_type = 'Cattle'
    `;
    let params: any[] = [];

    // Filter by gender if specified
    if (gender) {
      query += ' AND gender = ?';
      params.push(gender);
    }

    query += ' ORDER BY name ASC';

    console.log('Executing query:', query, 'with params:', params);
    const [rows] = await pool.execute(query, params);
    console.log(`Found ${(rows as any[]).length} cattle matching criteria`);

    // Calculate statistics
    const cattle = rows as any[];
    const stats = {
      total: cattle.length,
      healthy: cattle.filter(c => c.status === 'Healthy').length,
      sick: cattle.filter(c => c.status === 'Sick').length,
      injured: cattle.filter(c => c.status === 'Injured').length,
      quarantined: cattle.filter(c => c.status === 'Quarantined').length,
      pregnant: cattle.filter(c => c.status === 'Pregnant').length,
      lactating: cattle.filter(c => c.status === 'Lactating').length,
      males: cattle.filter(c => c.gender === 'Male').length,
      females: cattle.filter(c => c.gender === 'Female').length
    };

    console.log('Calculated stats:', stats);

    // Return both cattle and stats
    return NextResponse.json({
      cattle: cattle,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching cattle:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cattle' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    const {
      name,
      tagNumber,
      breed,
      gender,
      birthDate,
      acquisitionDate,
      status = 'Healthy',
      weight,
      color,
      markings,
      sire,
      dam,
      purchasePrice,
      isRegistered,
      registrationNumber,
      notes,
    } = body;

    const [result] = await pool.execute(
      `INSERT INTO animals (
        tag_number,
        name,
        animal_type,
        breed,
        gender,
        birth_date,
        acquisition_date,
        status,
        weight,
        color,
        markings,
        sire,
        dam,
        purchase_price,
        is_registered,
        registration_number,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tagNumber,
        name,
        'Cattle',
        breed,
        gender,
        birthDate || null,
        acquisitionDate || null,
        status,
        weight || null,
        color || null,
        markings || null,
        sire || null,
        dam || null,
        purchasePrice || null,
        isRegistered ? 1 : 0,
        registrationNumber || null,
        notes || null,
      ]
    );

    return NextResponse.json({
      message: 'Cattle added successfully',
      cattleId: (result as any).insertId
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error adding cattle:', error);
    
    // Handle duplicate tag number error
    if (error.code === 'ER_DUP_ENTRY') {
      return NextResponse.json(
        { error: 'Tag number already exists. Please use a unique tag number.' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to add cattle' },
      { status: 500 }
    );
  }
}
