import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool (more efficient than creating a new connection for each request)
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received heat cycle data:', data);

    // Validate required fields
    if (!data.goat_id || !data.heat_date) {
      console.log('Missing required fields:', { goat_id: data.goat_id, heat_date: data.heat_date });
      return NextResponse.json(
        { error: 'Missing required fields: goat_id and heat_date are required' },
        { status: 400 }
      );
    }

    // Process signs data - handle all possible formats
    let processedSigns = '';

    if (typeof data.signs === 'string') {
      // If it's already a string, check if it looks like JSON
      if (data.signs.startsWith('{') && data.signs.endsWith('}')) {
        try {
          // Try to parse it as JSON
          const signsObj = JSON.parse(data.signs);
          processedSigns = Object.entries(signsObj)
            .filter(([_, value]) => value === true)
            .map(([key]) => key)
            .join(',');
        } catch (e) {
          // If it's not valid JSON, use as is (might be comma-separated already)
          processedSigns = data.signs;
        }
      } else {
        // Use as is (likely already comma-separated)
        processedSigns = data.signs;
      }
    } else if (typeof data.signs === 'object' && data.signs !== null) {
      // If it's an object, extract true values
      processedSigns = Object.entries(data.signs)
        .filter(([_, value]) => value === true)
        .map(([key]) => key)
        .join(',');
    }

    console.log('Processed signs:', processedSigns);
    console.log('Attempting to insert heat cycle with goat_id:', data.goat_id);

    // Insert all data into the heat_cycles table
    const [result] = await pool.execute(
      `INSERT INTO heat_cycles (
        goat_id,
        heat_date,
        notes,
        intensity,
        signs,
        breeding_scheduled,
        planned_breeding_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        data.goat_id,
        data.heat_date,
        data.notes || null,
        data.intensity || 'moderate',
        processedSigns || null,
        data.breeding_scheduled || 0,
        data.planned_breeding_date || null
      ]
    );

    // Get the inserted ID
    const heatCycleId = result.insertId;
    console.log('Heat cycle inserted with ID:', heatCycleId);

    return NextResponse.json({
      success: true,
      message: 'Heat cycle recorded successfully',
      id: heatCycleId
    });
  } catch (error) {
    console.error('Error recording heat cycle:', error);
    return NextResponse.json(
      { error: `Failed to record heat cycle: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  let connection;

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || 100;
    const offset = searchParams.get('offset') || 0;
    const goatId = searchParams.get('goat_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    // Get a connection from the pool
    connection = await pool.getConnection();

    try {
      // Build the query with filters
      let query = `
        SELECT
          hc.id,
          hc.goat_id,
          g.name AS goat_name,
          g.tag_number,
          g.breed,
          hc.heat_date,
          DATE_FORMAT(hc.heat_date, '%Y-%m-%d') as formatted_date,
          hc.intensity,
          hc.signs,
          hc.notes,
          hc.breeding_scheduled,
          hc.planned_breeding_date,
          hc.created_at
        FROM heat_cycles hc
        LEFT JOIN goats g ON hc.goat_id = g.id
        WHERE 1=1
      `;

      const queryParams = [];

      // Add filters if provided
      if (goatId) {
        query += ' AND hc.goat_id = ?';
        queryParams.push(goatId);
      }

      if (startDate) {
        query += ' AND hc.heat_date >= ?';
        queryParams.push(startDate);
      }

      if (endDate) {
        query += ' AND hc.heat_date <= ?';
        queryParams.push(endDate);
      }

      // Add ordering
      query += ' ORDER BY hc.heat_date DESC, hc.id DESC';

      // Add pagination
      query += ' LIMIT ? OFFSET ?';
      queryParams.push(Number(limit), Number(offset));

      // Execute the query
      const [heatCycles] = await connection.execute(query, queryParams);

      // Get count of does currently in heat - simplified to just count rows in the heat_cycles table
      const [doesInHeatResult] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM heat_cycles
        WHERE heat_date >= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
      `);

      const doesInHeat = doesInHeatResult[0]?.count || 0;

      // Get count of total heat cycles - simplified to just count all rows
      const [totalCountResult] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM heat_cycles
      `);
      const totalCount = totalCountResult[0]?.count || 0;

      // Get average cycle length for each doe
      const [cycleLengths] = await connection.execute(`
        SELECT
          goat_id,
          g.name AS goat_name,
          AVG(DATEDIFF(next_heat, heat_date)) AS avg_cycle_length
        FROM (
          SELECT
            hc1.goat_id,
            hc1.heat_date,
            MIN(hc2.heat_date) AS next_heat
          FROM
            heat_cycles hc1
          LEFT JOIN
            heat_cycles hc2 ON hc1.goat_id = hc2.goat_id AND hc2.heat_date > hc1.heat_date
          GROUP BY
            hc1.id, hc1.goat_id, hc1.heat_date
        ) AS cycles
        LEFT JOIN goats g ON cycles.goat_id = g.id
        WHERE next_heat IS NOT NULL
        GROUP BY goat_id, g.name
      `);

      // Get upcoming heat predictions
      const [upcomingHeat] = await connection.execute(`
        SELECT
          hc.goat_id,
          g.name AS goat_name,
          g.tag_number,
          MAX(hc.heat_date) AS last_heat_date,
          DATE_ADD(MAX(hc.heat_date), INTERVAL 21 DAY) AS predicted_next_heat
        FROM
          heat_cycles hc
        JOIN
          goats g ON hc.goat_id = g.id
        WHERE
          g.status = 'Active' AND g.gender = 'Female'
        GROUP BY
          hc.goat_id, g.name, g.tag_number
        HAVING
          predicted_next_heat >= CURDATE() AND predicted_next_heat <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        ORDER BY
          predicted_next_heat ASC
      `);

      // Process the results
      const processedHeatCycles = heatCycles.map(cycle => ({
        ...cycle,
        signs: cycle.signs ? cycle.signs.split(',') : [],
        breeding_scheduled: Boolean(cycle.breeding_scheduled)
      }));

      // Return the data
      return NextResponse.json({
        heatCycles: processedHeatCycles,
        stats: {
          totalCount,
          doesInHeat,
          cycleLengths: cycleLengths || [],
          upcomingHeat: upcomingHeat || []
        }
      });

    } catch (queryError) {
      console.error('Error executing database query:', queryError);
      return NextResponse.json(
        { error: 'Database query error: ' + queryError.message },
        { status: 500 }
      );
    } finally {
      // Release the connection
      if (connection) connection.release();
    }
  } catch (error) {
    console.error('Error in heat cycles API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch heat cycles: ' + error.message },
      { status: 500 }
    );
  }
}







