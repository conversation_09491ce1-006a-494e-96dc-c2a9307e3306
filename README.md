# Goat Farm Management System

A comprehensive management system for goat farms, built with Next.js.

## Dependencies

### Core Dependencies

- **Next.js**: React framework for building the application
- **React**: UI library
- **TypeScript**: Type-safe JavaScript
- **MySQL**: Database for storing farm data

### Security Dependencies

- **bcrypt**: Used for secure password hashing
  - Required for secure user authentication
  - Install with: `npm install bcrypt`
  - If not installed, the system will use a fallback hashing method (not recommended for production)

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # If you encounter peer dependency issues, use:
   npm install --legacy-peer-deps
   ```

3. Install bcrypt for secure password hashing:
   ```bash
   npm install bcrypt
   npm install --save-dev @types/bcrypt
   ```

4. Set up environment variables:
   ```
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=goat_management
   ```

5. Run database migrations:
   ```bash
   node scripts/run_migration.js
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

## Features

- User management with role-based access control
- Goat inventory management
- Health record tracking
- Breeding management
- Financial transaction tracking
- Feeding management
- Reporting and analytics

## User Management

The system uses a role-based access control system with the following roles:

- **Admin**: Full access to all features
- **Manager**: Access to most features except user management
- **Staff**: Limited access to daily operations
- **Viewer**: Read-only access

Users can be activated or deactivated rather than deleted, preserving data integrity.

## Database Schema

See [database_schema.md](docs/database_schema.md) for details on the database structure.
