"use client"

import { useState, useEffect, useRef } from "react"
import "./print-styles.css"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"
import {
  LucideBarChart2,
  LucideDownload,
  LucideFilter,
  LucidePrinter,
  LucideRefreshCw,
  LucideCalendarRange,
  LucideCoins,
  LucideHeart,
  LucideUsers,
  LucideCalendarClock,
  LucideLoader2,
  LucideAlertCircle,
  LucidePackage,
  LucideFileText,
  LucideFileSpreadsheet,
  LucideFileType2,
  LucideChevronDown,
  LucideCheck,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import ReportChart from "@/components/report-chart"

export default function ReportsPage() {
  const { toast } = useToast()
  const [dateRange, setDateRange] = useState("30")
  const [reportType, setReportType] = useState("all")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPrinting, setIsPrinting] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const reportContentRef = useRef<HTMLDivElement>(null)

  // State for report data
  const [reportData, setReportData] = useState({
    summary: {
      totalRevenue: 0,
      totalExpenses: 0,
      netProfit: 0,
      profitMargin: 0,
      healthIssues: 0,
      birthRate: 0,
      inventoryValue: 0,
      feedCosts: 0,
    },
    financial: {
      monthlyData: [],
      expenseBreakdown: [],
      revenueSources: []
    },
    health: {
      monthlyData: [],
      issuesBreakdown: []
    },
    breeding: {
      monthlyData: [],
      breedingByBreed: [],
      kidsPerBirth: []
    },
    goats: {
      breedDistribution: [],
      genderDistribution: []
    },
    inventory: {}
  })

  // Fetch report data from API
  const fetchReportData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/reports/dashboard?dateRange=${dateRange}&reportType=${reportType}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch report data: ${response.status}`)
      }

      const data = await response.json()
      console.log('Fetched report data:', data)
      setReportData(data)
    } catch (err: any) {
      console.error('Error fetching report data:', err)
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data when component mounts or filters change
  useEffect(() => {
    fetchReportData()
  }, [dateRange, reportType])

  // Extract summary data for easier access
  const summaryData = reportData.summary

  // Handle print report
  const handlePrintReport = () => {
    // Ensure this only runs on the client side
    if (typeof window === 'undefined') return;

    setIsPrinting(true)

    // Add a small delay to ensure the UI updates before printing
    setTimeout(() => {
      window.print()
      setIsPrinting(false)
    }, 300)
  }

  // Handle export report
  const handleExportReport = (format: string) => {
    // Ensure this only runs on the client side
    if (typeof window === 'undefined') return;

    setIsExporting(true)

    try {
      // Get the report title based on the current tab
      const currentDate = new Date().toISOString().split('T')[0];
      const reportTitle = `Goat_Farm_${reportType.charAt(0).toUpperCase() + reportType.slice(1)}_Report_${currentDate}`

      if (format === 'pdf') {
        // For PDF, we'll use the print functionality to save as PDF
        toast({
          title: "Preparing PDF",
          description: "Your browser's save as PDF dialog will open shortly.",
        })

        setTimeout(() => {
          window.print()
        }, 300)
      } else if (format === 'csv') {
        // For CSV, we'll create a simple CSV file with the data
        exportAsCSV(reportTitle)
      } else if (format === 'excel') {
        // For Excel, we'll create a simple Excel-compatible CSV file
        exportAsCSV(reportTitle, true)
      }
    } catch (err: any) {
      console.error('Error exporting report:', err)
      toast({
        title: "Export Failed",
        description: err.message || "Failed to export report. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Export as CSV
  const exportAsCSV = (filename: string, forExcel: boolean = false) => {
    // Use useEffect to ensure this only runs on the client side
    if (typeof window === 'undefined') return;

    // Create CSV content based on the current report type
    let csvContent = ""

    // Add headers
    if (reportType === 'financial' || reportType === 'all') {
      csvContent += "Month,Revenue,Expenses,Profit\n"

      // Add data
      reportData.financial.monthlyData?.forEach((item: any) => {
        csvContent += `${item.month},${item.revenue},${item.expenses},${item.profit}\n`
      })

      csvContent += "\n\nExpense Categories\n"
      csvContent += "Category,Amount\n"

      reportData.financial.expenseBreakdown?.forEach((item: any) => {
        csvContent += `${item.category},${item.total}\n`
      })

      csvContent += "\n\nRevenue Sources\n"
      csvContent += "Category,Amount\n"

      reportData.financial.revenueSources?.forEach((item: any) => {
        csvContent += `${item.category},${item.total}\n`
      })
    }

    if (reportType === 'health' || reportType === 'all') {
      if (csvContent) csvContent += "\n\n"
      csvContent += "Health Records\n"
      csvContent += "Month,Illnesses,Vaccinations,Treatments\n"

      reportData.health.monthlyData?.forEach((item: any) => {
        csvContent += `${item.month},${item.illnesses || 0},${item.vaccinations || 0},${item.treatments || 0}\n`
      })

      csvContent += "\n\nHealth Issues Breakdown\n"
      csvContent += "Type,Count\n"

      reportData.health.issuesBreakdown?.forEach((item: any) => {
        csvContent += `${item.record_type},${item.count}\n`
      })
    }

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: forExcel ? 'application/vnd.ms-excel' : 'text/csv' })

    // Create a download link
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.${forExcel ? 'xls' : 'csv'}`
    document.body.appendChild(a)
    a.click()

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }, 100)

    toast({
      title: "Export Successful",
      description: `Report has been exported as ${forExcel ? 'Excel' : 'CSV'} file.`,
      variant: "default",
    })
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6" ref={reportContentRef}>
        {/* Print styles - only visible when printing */}
        <style jsx global>{`
          @media print {
            body * {
              visibility: hidden;
            }
            #report-content, #report-content * {
              visibility: visible;
            }
            #report-content {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              padding: 20px;
            }
            .no-print {
              display: none !important;
            }
            .print-break-after {
              page-break-after: always;
            }
            .print-full-width {
              width: 100% !important;
            }
          }
        `}</style>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Reports & Analytics</h1>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              className="btn-outline-accent"
              onClick={handlePrintReport}
              disabled={isLoading || isPrinting}
            >
              {isPrinting ? (
                <LucideLoader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <LucidePrinter className="mr-2 h-4 w-4" />
              )}
              {isPrinting ? "Printing..." : "Print"}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all duration-300"
                  disabled={isLoading || isExporting}
                >
                  {isExporting ? (
                    <LucideLoader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <LucideDownload className="mr-2 h-4 w-4" />
                  )}
                  {isExporting ? "Exporting..." : "Export Reports"}
                  <LucideChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleExportReport('pdf')} disabled={isExporting}>
                  <LucideFileType2 className="mr-2 h-4 w-4 text-red-500" />
                  <span>Export as PDF</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExportReport('csv')} disabled={isExporting}>
                  <LucideFileText className="mr-2 h-4 w-4 text-blue-500" />
                  <span>Export as CSV</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExportReport('excel')} disabled={isExporting}>
                  <LucideFileSpreadsheet className="mr-2 h-4 w-4 text-green-600" />
                  <span>Export as Excel</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Filters */}
        <Card className="overflow-hidden no-print">
          <CardHeader className="p-4 pb-0">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle>Report Filters</CardTitle>
              <div className="flex items-center gap-2">
                <LucideFilter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Customize your reports</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <Select value={dateRange} onValueChange={setDateRange} disabled={isLoading}>
                  <SelectTrigger className="w-full">
                    <div className="flex items-center gap-2">
                      <LucideCalendarRange className="h-4 w-4 text-purple-500" />
                      <SelectValue placeholder="Date Range" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="365">Last year</SelectItem>
                    <SelectItem value="custom">Custom range</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={reportType} onValueChange={setReportType} disabled={isLoading}>
                  <SelectTrigger className="w-full">
                    <div className="flex items-center gap-2">
                      <LucideBarChart2 className="h-4 w-4 text-purple-500" />
                      <SelectValue placeholder="Report Type" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Reports</SelectItem>
                    <SelectItem value="financial">Financial</SelectItem>
                    <SelectItem value="health">Health</SelectItem>
                    <SelectItem value="breeding">Breeding</SelectItem>
                    <SelectItem value="inventory">Inventory</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                className="flex items-center gap-2 border-purple-500 text-purple-600 hover:bg-purple-50 hover:text-purple-700"
                onClick={() => {
                  setDateRange("30")
                  setReportType("all")
                  // Fetch data will be triggered by the useEffect when state changes
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <LucideLoader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <LucideRefreshCw className="h-4 w-4" />
                )}
                {isLoading ? "Loading..." : "Reset Filters"}
              </Button>
            </div>

            {/* Error message */}
            {error && (
              <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
                <div className="flex items-center gap-2">
                  <LucideAlertCircle className="h-5 w-5" />
                  <span className="font-medium">Error Loading Report Data</span>
                </div>
                <p className="mt-1">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 border-red-300 text-red-700 hover:bg-red-100"
                  onClick={fetchReportData}
                >
                  <LucideRefreshCw className="h-3 w-3 mr-2" />
                  Retry
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Report Content - This is what gets printed/exported */}
        <div id="report-content">
          {/* Report Header - Only visible when printing */}
          <div className="hidden print:block mb-8">
            <h1 className="text-3xl font-bold text-center mb-2">Goat Farm Management</h1>
            <h2 className="text-xl font-semibold text-center mb-4">
              {reportType === 'all' ? 'Complete' : reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report
            </h2>
            <div className="text-center text-gray-600 mb-2">
              Report Date: {new Date().toISOString().split('T')[0]}
            </div>
            <div className="text-center text-gray-600">
              Period: Last {dateRange} days
            </div>
            <hr className="my-4 border-gray-300" />
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="dashboard-card-primary">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-muted-foreground">Revenue</div>
                    {isLoading ? (
                      <div className="h-8 w-24 bg-gray-200 animate-pulse rounded mt-1"></div>
                    ) : (
                      <div className="text-2xl font-bold text-green-600">MWK {summaryData.totalRevenue.toLocaleString()}</div>
                    )}
                  </div>
                  <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                    {isLoading ? (
                      <LucideLoader2 className="h-5 w-5 text-green-600 animate-spin" />
                    ) : (
                      <LucideCoins className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="dashboard-card-secondary">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-muted-foreground">Profit Margin</div>
                    {isLoading ? (
                      <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-1"></div>
                    ) : (
                      <div className="text-2xl font-bold text-emerald-600">{summaryData.profitMargin}%</div>
                    )}
                  </div>
                  <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                    {isLoading ? (
                      <LucideLoader2 className="h-5 w-5 text-emerald-600 animate-spin" />
                    ) : (
                      <LucideBarChart2 className="h-5 w-5 text-emerald-600" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="dashboard-card-accent">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-muted-foreground">Health Records</div>
                    {isLoading ? (
                      <div className="h-8 w-12 bg-gray-200 animate-pulse rounded mt-1"></div>
                    ) : (
                      <div className="text-2xl font-bold text-red-600">{summaryData.healthIssues}</div>
                    )}
                  </div>
                  <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                    {isLoading ? (
                      <LucideLoader2 className="h-5 w-5 text-red-600 animate-spin" />
                    ) : (
                      <LucideHeart className="h-5 w-5 text-red-600" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="dashboard-card-amber">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-muted-foreground">Birth Rate</div>
                    {isLoading ? (
                      <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mt-1"></div>
                    ) : (
                      <div className="text-2xl font-bold text-blue-600">{summaryData.birthRate}%</div>
                    )}
                  </div>
                  <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    {isLoading ? (
                      <LucideLoader2 className="h-5 w-5 text-blue-600 animate-spin" />
                    ) : (
                      <LucideCalendarClock className="h-5 w-5 text-blue-600" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Report Tabs */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5 p-1 bg-gradient-to-r from-violet-50 via-purple-50 to-fuchsia-50 rounded-xl">
              <TabsTrigger
                value="overview"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-500 data-[state=active]:text-white transition-all duration-300 hover:text-violet-700"
              >
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="financial"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-fuchsia-500 data-[state=active]:text-white transition-all duration-300 hover:text-purple-700"
              >
                Financial
              </TabsTrigger>
              <TabsTrigger
                value="health"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-fuchsia-500 data-[state=active]:to-pink-500 data-[state=active]:text-white transition-all duration-300 hover:text-fuchsia-700"
              >
                Health
              </TabsTrigger>
              <TabsTrigger
                value="breeding"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-rose-500 data-[state=active]:text-white transition-all duration-300 hover:text-pink-700"
              >
                Breeding
              </TabsTrigger>
              <TabsTrigger
                value="inventory"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-rose-500 data-[state=active]:to-red-500 data-[state=active]:text-white transition-all duration-300 hover:text-rose-700"
              >
                Inventory
              </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Farm Performance Overview</CardTitle>
                <CardDescription>
                  Summary of key metrics across all farm operations for the last {dateRange} days
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[300px] w-full">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                    </div>
                  ) : (
                    <ReportChart
                      type="bar"
                      title="Monthly Performance"
                      data={{
                        labels: reportData.financial.monthlyData?.map((item: any) => item.month) || [],
                        datasets: [
                          {
                            label: "Revenue",
                            data: reportData.financial.monthlyData?.map((item: any) => item.revenue) || [],
                            backgroundColor: "rgba(34, 197, 94, 0.5)",
                            borderColor: "rgb(34, 197, 94)",
                          },
                          {
                            label: "Expenses",
                            data: reportData.financial.monthlyData?.map((item: any) => item.expenses) || [],
                            backgroundColor: "rgba(239, 68, 68, 0.5)",
                            borderColor: "rgb(239, 68, 68)",
                          },
                        ],
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Goat Population</CardTitle>
                  <CardDescription>Distribution by breed and gender</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                      </div>
                    ) : (
                      <ReportChart
                        type="pie"
                        title="Goat Breeds"
                        data={{
                          labels: reportData.goats.breedDistribution?.map((item: any) => item.breed) || [],
                          datasets: [
                            {
                              data: reportData.goats.breedDistribution?.map((item: any) => item.count) || [],
                              backgroundColor: [
                                "rgba(34, 197, 94, 0.7)",
                                "rgba(16, 185, 129, 0.7)",
                                "rgba(20, 184, 166, 0.7)",
                                "rgba(6, 182, 212, 0.7)",
                                "rgba(59, 130, 246, 0.7)",
                                "rgba(99, 102, 241, 0.7)",
                                "rgba(139, 92, 246, 0.7)",
                              ],
                              borderColor: [
                                "rgb(34, 197, 94)",
                                "rgb(16, 185, 129)",
                                "rgb(20, 184, 166)",
                                "rgb(6, 182, 212)",
                                "rgb(59, 130, 246)",
                                "rgb(99, 102, 241)",
                                "rgb(139, 92, 246)",
                              ],
                            },
                          ],
                        }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Health Trends</CardTitle>
                  <CardDescription>Health incidents over time</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                      </div>
                    ) : (
                      <ReportChart
                        type="line"
                        title="Health Incidents"
                        data={{
                          labels: reportData.health.monthlyData?.map((item: any) => item.month) || [],
                          datasets: [
                            {
                              label: "Illnesses",
                              data: reportData.health.monthlyData?.map((item: any) => item.illnesses) || [],
                              borderColor: "rgb(239, 68, 68)",
                              backgroundColor: "rgba(239, 68, 68, 0.1)",
                              tension: 0.3,
                              fill: true,
                            },
                            {
                              label: "Treatments",
                              data: reportData.health.monthlyData?.map((item: any) => item.treatments) || [],
                              borderColor: "rgb(59, 130, 246)",
                              backgroundColor: "rgba(59, 130, 246, 0.1)",
                              tension: 0.3,
                              fill: true,
                            },
                          ],
                        }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Tab */}
          <TabsContent value="financial" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Financial Performance</CardTitle>
                <CardDescription>Revenue, expenses, and profit analysis</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[300px] w-full">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                    </div>
                  ) : (
                    <ReportChart
                      type="bar"
                      title="Financial Performance"
                      data={{
                        labels: reportData.financial.monthlyData?.map((item: any) => item.month) || [],
                        datasets: [
                          {
                            label: "Revenue",
                            data: reportData.financial.monthlyData?.map((item: any) => item.revenue) || [],
                            backgroundColor: "rgba(34, 197, 94, 0.5)",
                            borderColor: "rgb(34, 197, 94)",
                          },
                          {
                            label: "Expenses",
                            data: reportData.financial.monthlyData?.map((item: any) => item.expenses) || [],
                            backgroundColor: "rgba(239, 68, 68, 0.5)",
                            borderColor: "rgb(239, 68, 68)",
                          },
                          {
                            label: "Profit",
                            data: reportData.financial.monthlyData?.map((item: any) => item.profit) || [],
                            backgroundColor: "rgba(59, 130, 246, 0.5)",
                            borderColor: "rgb(59, 130, 246)",
                          },
                        ],
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Expense Breakdown</CardTitle>
                  <CardDescription>Where your money is going</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                      </div>
                    ) : (
                      <ReportChart
                        type="doughnut"
                        title="Expense Categories"
                        data={{
                          labels: reportData.financial.expenseBreakdown?.map((item: any) => item.category) || [],
                          datasets: [
                            {
                              data: reportData.financial.expenseBreakdown?.map((item: any) => item.total) || [],
                              backgroundColor: [
                                "rgba(234, 179, 8, 0.7)",
                                "rgba(239, 68, 68, 0.7)",
                                "rgba(59, 130, 246, 0.7)",
                                "rgba(168, 85, 247, 0.7)",
                                "rgba(107, 114, 128, 0.7)",
                                "rgba(16, 185, 129, 0.7)",
                                "rgba(236, 72, 153, 0.7)",
                              ],
                              borderColor: [
                                "rgb(234, 179, 8)",
                                "rgb(239, 68, 68)",
                                "rgb(59, 130, 246)",
                                "rgb(168, 85, 247)",
                                "rgb(107, 114, 128)",
                                "rgb(16, 185, 129)",
                                "rgb(236, 72, 153)",
                              ],
                            },
                          ],
                        }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue Sources</CardTitle>
                  <CardDescription>Where your income comes from</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                      </div>
                    ) : (
                      <ReportChart
                        type="doughnut"
                        title="Revenue Sources"
                        data={{
                          labels: reportData.financial.revenueSources?.map((item: any) => item.category) || [],
                          datasets: [
                            {
                              data: reportData.financial.revenueSources?.map((item: any) => item.total) || [],
                              backgroundColor: [
                                "rgba(34, 197, 94, 0.7)",
                                "rgba(16, 185, 129, 0.7)",
                                "rgba(20, 184, 166, 0.7)",
                                "rgba(6, 182, 212, 0.7)",
                                "rgba(59, 130, 246, 0.7)",
                                "rgba(99, 102, 241, 0.7)",
                                "rgba(139, 92, 246, 0.7)",
                              ],
                              borderColor: [
                                "rgb(34, 197, 94)",
                                "rgb(16, 185, 129)",
                                "rgb(20, 184, 166)",
                                "rgb(6, 182, 212)",
                                "rgb(59, 130, 246)",
                                "rgb(99, 102, 241)",
                                "rgb(139, 92, 246)",
                              ],
                            },
                          ],
                        }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Health Tab */}
          <TabsContent value="health" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Health Statistics</CardTitle>
                <CardDescription>Health incidents and treatments</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[300px] w-full">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                    </div>
                  ) : (
                    <ReportChart
                      type="line"
                      title="Health Trends"
                      data={{
                        labels: reportData.health.monthlyData?.map((item: any) => item.month) || [],
                        datasets: [
                          {
                            label: "Illnesses",
                            data: reportData.health.monthlyData?.map((item: any) => item.illnesses) || [],
                            borderColor: "rgb(239, 68, 68)",
                            backgroundColor: "rgba(239, 68, 68, 0.1)",
                            tension: 0.3,
                            fill: true,
                          },
                          {
                            label: "Vaccinations",
                            data: reportData.health.monthlyData?.map((item: any) => item.vaccinations) || [],
                            borderColor: "rgb(34, 197, 94)",
                            backgroundColor: "rgba(34, 197, 94, 0.1)",
                            tension: 0.3,
                            fill: true,
                          },
                          {
                            label: "Treatments",
                            data: reportData.health.monthlyData?.map((item: any) => item.treatments) || [],
                            borderColor: "rgb(59, 130, 246)",
                            backgroundColor: "rgba(59, 130, 246, 0.1)",
                            tension: 0.3,
                            fill: true,
                          },
                        ],
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Common Health Issues</CardTitle>
                  <CardDescription>Most frequent health problems</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
                      </div>
                    ) : (
                      <ReportChart
                        type="pie"
                        title="Health Issues"
                        data={{
                          labels: reportData.health.issuesBreakdown?.map((item: any) => item.record_type) || [],
                          datasets: [
                            {
                              data: reportData.health.issuesBreakdown?.map((item: any) => item.count) || [],
                              backgroundColor: [
                                "rgba(239, 68, 68, 0.7)",
                                "rgba(249, 115, 22, 0.7)",
                                "rgba(234, 179, 8, 0.7)",
                                "rgba(168, 85, 247, 0.7)",
                                "rgba(107, 114, 128, 0.7)",
                                "rgba(16, 185, 129, 0.7)",
                                "rgba(236, 72, 153, 0.7)",
                              ],
                              borderColor: [
                                "rgb(239, 68, 68)",
                                "rgb(249, 115, 22)",
                                "rgb(234, 179, 8)",
                                "rgb(168, 85, 247)",
                                "rgb(107, 114, 128)",
                                "rgb(16, 185, 129)",
                                "rgb(236, 72, 153)",
                              ],
                            },
                          ],
                        }}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Treatment Effectiveness</CardTitle>
                  <CardDescription>Recovery rates by treatment type</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    <ReportChart
                      type="bar"
                      title="Treatment Effectiveness"
                      data={{
                        labels: ["Antibiotics", "Dewormers", "Supplements", "Topical", "Other"],
                        datasets: [
                          {
                            label: "Success Rate (%)",
                            data: [85, 92, 78, 88, 70],
                            backgroundColor: "rgba(34, 197, 94, 0.7)",
                            borderColor: "rgb(34, 197, 94)",
                          },
                        ],
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Breeding Tab */}
          <TabsContent value="breeding" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Breeding Performance</CardTitle>
                <CardDescription>Breeding success rates and kidding statistics</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[300px] w-full">
                  <ReportChart
                    type="bar"
                    title="Breeding Statistics"
                    data={{
                      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                      datasets: [
                        {
                          label: "Breeding Attempts",
                          data: [8, 6, 10, 7, 9, 12],
                          backgroundColor: "rgba(168, 85, 247, 0.5)",
                          borderColor: "rgb(168, 85, 247)",
                        },
                        {
                          label: "Successful Pregnancies",
                          data: [6, 5, 8, 6, 7, 10],
                          backgroundColor: "rgba(59, 130, 246, 0.5)",
                          borderColor: "rgb(59, 130, 246)",
                        },
                        {
                          label: "Kids Born",
                          data: [10, 8, 14, 10, 12, 18],
                          backgroundColor: "rgba(34, 197, 94, 0.5)",
                          borderColor: "rgb(34, 197, 94)",
                        },
                      ],
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Breeding Success by Breed</CardTitle>
                  <CardDescription>Success rates for different goat breeds</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    <ReportChart
                      type="bar"
                      title="Breeding Success by Breed"
                      data={{
                        labels: ["Boer", "Alpine", "Nubian", "Saanen", "Mixed"],
                        datasets: [
                          {
                            label: "Success Rate (%)",
                            data: [88, 75, 92, 80, 85],
                            backgroundColor: [
                              "rgba(34, 197, 94, 0.7)",
                              "rgba(16, 185, 129, 0.7)",
                              "rgba(20, 184, 166, 0.7)",
                              "rgba(6, 182, 212, 0.7)",
                              "rgba(59, 130, 246, 0.7)",
                            ],
                            borderColor: [
                              "rgb(34, 197, 94)",
                              "rgb(16, 185, 129)",
                              "rgb(20, 184, 166)",
                              "rgb(6, 182, 212)",
                              "rgb(59, 130, 246)",
                            ],
                          },
                        ],
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Kids per Birth</CardTitle>
                  <CardDescription>Average number of kids per birth by breed</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    <ReportChart
                      type="bar"
                      title="Kids per Birth"
                      data={{
                        labels: ["Boer", "Alpine", "Nubian", "Saanen", "Mixed"],
                        datasets: [
                          {
                            label: "Average Kids",
                            data: [1.8, 2.1, 2.3, 1.9, 2.0],
                            backgroundColor: [
                              "rgba(249, 115, 22, 0.7)",
                              "rgba(234, 179, 8, 0.7)",
                              "rgba(168, 85, 247, 0.7)",
                              "rgba(59, 130, 246, 0.7)",
                              "rgba(107, 114, 128, 0.7)",
                            ],
                            borderColor: [
                              "rgb(249, 115, 22)",
                              "rgb(234, 179, 8)",
                              "rgb(168, 85, 247)",
                              "rgb(59, 130, 246)",
                              "rgb(107, 114, 128)",
                            ],
                          },
                        ],
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Inventory Tab */}
          <TabsContent value="inventory" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Inventory Value</CardTitle>
                <CardDescription>Value of inventory over time</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[300px] w-full">
                  <ReportChart
                    type="line"
                    title="Inventory Value"
                    data={{
                      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                      datasets: [
                        {
                          label: "Feed Value",
                          data: [3000, 2800, 3200, 3500, 3300, 3800],
                          borderColor: "rgb(234, 179, 8)",
                          backgroundColor: "rgba(234, 179, 8, 0.1)",
                          tension: 0.3,
                          fill: true,
                        },
                        {
                          label: "Equipment Value",
                          data: [8000, 8000, 8500, 8500, 9000, 9000],
                          borderColor: "rgb(59, 130, 246)",
                          backgroundColor: "rgba(59, 130, 246, 0.1)",
                          tension: 0.3,
                          fill: true,
                        },
                        {
                          label: "Livestock Value",
                          data: [12000, 12500, 13000, 14000, 14500, 15000],
                          borderColor: "rgb(34, 197, 94)",
                          backgroundColor: "rgba(34, 197, 94, 0.1)",
                          tension: 0.3,
                          fill: true,
                        },
                      ],
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="grid md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Feed Inventory</CardTitle>
                  <CardDescription>Current feed stock levels</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    <ReportChart
                      type="bar"
                      title="Feed Inventory"
                      data={{
                        labels: ["Hay", "Grain", "Alfalfa", "Minerals", "Supplements"],
                        datasets: [
                          {
                            label: "Current Stock (kg)",
                            data: [750, 150, 225, 50, 30],
                            backgroundColor: [
                              "rgba(234, 179, 8, 0.7)",
                              "rgba(249, 115, 22, 0.7)",
                              "rgba(34, 197, 94, 0.7)",
                              "rgba(16, 185, 129, 0.7)",
                              "rgba(20, 184, 166, 0.7)",
                            ],
                            borderColor: [
                              "rgb(234, 179, 8)",
                              "rgb(249, 115, 22)",
                              "rgb(34, 197, 94)",
                              "rgb(16, 185, 129)",
                              "rgb(20, 184, 166)",
                            ],
                          },
                        ],
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Equipment Utilization</CardTitle>
                  <CardDescription>Usage rates of farm equipment</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="h-[250px] w-full">
                    <ReportChart
                      type="doughnut"
                      title="Equipment Usage"
                      data={{
                        labels: ["Milking Equipment", "Feeding Systems", "Fencing", "Shelters", "Tools"],
                        datasets: [
                          {
                            data: [35, 25, 15, 15, 10],
                            backgroundColor: [
                              "rgba(59, 130, 246, 0.7)",
                              "rgba(168, 85, 247, 0.7)",
                              "rgba(236, 72, 153, 0.7)",
                              "rgba(239, 68, 68, 0.7)",
                              "rgba(107, 114, 128, 0.7)",
                            ],
                            borderColor: [
                              "rgb(59, 130, 246)",
                              "rgb(168, 85, 247)",
                              "rgb(236, 72, 153)",
                              "rgb(239, 68, 68)",
                              "rgb(107, 114, 128)",
                            ],
                          },
                        ],
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

          {/* Report Footer - Only visible when printing */}
          <div className="hidden print:block mt-8 pt-4 border-t border-gray-300">
            <div className="text-center text-gray-600 text-sm">
              Generated on {new Date().toISOString().split('T')[0]} | Goat Farm Management System
            </div>
            <div className="text-center text-gray-500 text-xs mt-1">
              This report contains confidential information. Please handle with care.
            </div>
          </div>
        </div> {/* End of report-content div */}

        {/* Recent Reports - Not included in print/export */}
        <Card className="no-print">
          <CardHeader>
            <CardTitle>Recent Reports</CardTitle>
            <CardDescription>Your recently generated reports</CardDescription>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg hover:from-violet-100 hover:to-purple-100 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-violet-100 flex items-center justify-center">
                    <LucideBarChart2 className="h-5 w-5 text-violet-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Monthly Financial Summary</h3>
                    <p className="text-sm text-muted-foreground">Generated on June 1, 2024</p>
                  </div>
                </div>
                <Badge className="bg-violet-100 text-violet-800 hover:bg-violet-200">Financial</Badge>
                <Button variant="ghost" size="sm" className="text-violet-600 hover:text-violet-700 hover:bg-violet-100">
                  <LucideDownload className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-fuchsia-50 to-pink-50 rounded-lg hover:from-fuchsia-100 hover:to-pink-100 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-fuchsia-100 flex items-center justify-center">
                    <LucideHeart className="h-5 w-5 text-fuchsia-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Quarterly Health Report</h3>
                    <p className="text-sm text-muted-foreground">Generated on May 15, 2024</p>
                  </div>
                </div>
                <Badge className="bg-fuchsia-100 text-fuchsia-800 hover:bg-fuchsia-200">Health</Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-fuchsia-600 hover:text-fuchsia-700 hover:bg-fuchsia-100"
                >
                  <LucideDownload className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg hover:from-pink-100 hover:to-rose-100 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                    <LucideUsers className="h-5 w-5 text-pink-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Breeding Success Analysis</h3>
                    <p className="text-sm text-muted-foreground">Generated on May 10, 2024</p>
                  </div>
                </div>
                <Badge className="bg-pink-100 text-pink-800 hover:bg-pink-200">Breeding</Badge>
                <Button variant="ghost" size="sm" className="text-pink-600 hover:text-pink-700 hover:bg-pink-100">
                  <LucideDownload className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

