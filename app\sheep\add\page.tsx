"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import DashboardLayout from "@/components/dashboard-layout"
import { addSheep } from "@/app/actions/sheep"
import { useFormStatus } from "react-dom"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { LucidePlus } from "lucide-react"
import { BackButton } from "@/components/ui/back-button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

function SubmitButton() {
  const { pending } = useFormStatus()

  return (
    <Button type="submit" disabled={pending} className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-md">
      {pending ? "Adding..." : "Add Sheep"}
    </Button>
  )
}

export default function AddSheepPage() {
  const router = useRouter()
  const [isRegistered, setIsRegistered] = useState(false)
  const [status, setStatus] = useState("Healthy")

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <BackButton
              href="/sheep"
              label="Back to Sheep"
              variant="ghost"
              className="mb-2"
            />
            <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">Add New Sheep</h1>
            <p className="text-muted-foreground mt-1">Enter the details of your new sheep</p>
          </div>
        </div>

        <Card className="border-t-4 border-t-emerald-500 shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50">
            <CardTitle className="flex items-center gap-2 text-emerald-700">
              <LucidePlus className="h-5 w-5 text-emerald-600" />
              Sheep Information
            </CardTitle>
            <CardDescription>Fill in the details below to register a new sheep in your flock</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <form action={async (formData) => {
              try {
                const result = await addSheep(formData)

                if (result.success) {
                  toast({
                    title: "Success",
                    description: result.message,
                  })
                  router.push("/sheep")
                } else {
                  // Handle server-side validation errors
                  toast({
                    title: "Error",
                    description: result.message,
                    variant: "destructive",
                  })
                }
              } catch (error) {
                toast({
                  title: "Error",
                  description: "Failed to add sheep. Please try again.",
                  variant: "destructive",
                })
              }
            }} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Tag Number*</label>
                  <Input
                    name="tagNumber"
                    placeholder="Enter tag number"
                    required
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Name*</label>
                  <Input
                    name="name"
                    placeholder="Enter sheep name"
                    required
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Breed*</label>
                  <Select name="breed" required>
                    <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                      <SelectValue placeholder="Select breed" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Merino">Merino</SelectItem>
                      <SelectItem value="Suffolk">Suffolk</SelectItem>
                      <SelectItem value="Dorper">Dorper</SelectItem>
                      <SelectItem value="Romney">Romney</SelectItem>
                      <SelectItem value="Corriedale">Corriedale</SelectItem>
                      <SelectItem value="Leicester Longwool">Leicester Longwool</SelectItem>
                      <SelectItem value="Border Leicester">Border Leicester</SelectItem>
                      <SelectItem value="Cheviot">Cheviot</SelectItem>
                      <SelectItem value="Jacob">Jacob</SelectItem>
                      <SelectItem value="Katahdin">Katahdin</SelectItem>
                      <SelectItem value="Barbados Black Belly">Barbados Black Belly</SelectItem>
                      <SelectItem value="East African (Local)">East African (Local)</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Gender*</label>
                  <Select name="gender" required>
                    <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Female">Female</SelectItem>
                      <SelectItem value="Male">Male</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Birth Date</label>
                  <Input
                    type="date"
                    name="birthDate"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Acquisition Date</label>
                  <Input
                    type="date"
                    name="acquisitionDate"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    name="status"
                    value={status}
                    onValueChange={setStatus}
                  >
                    <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Healthy">Healthy</SelectItem>
                      <SelectItem value="Sick">Sick</SelectItem>
                      <SelectItem value="Injured">Injured</SelectItem>
                      <SelectItem value="Quarantined">Quarantined</SelectItem>
                      <SelectItem value="Deceased">Deceased</SelectItem>
                      <SelectItem value="Pregnant">Pregnant</SelectItem>
                      <SelectItem value="Lactating">Lactating</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Weight (kg)</label>
                  <Input
                    type="number"
                    step="0.01"
                    name="weight"
                    placeholder="Enter weight"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Color</label>
                  <Input
                    name="color"
                    placeholder="Enter color"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Markings</label>
                  <Input
                    name="markings"
                    placeholder="Enter markings"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Sire (Father)</label>
                  <Input
                    name="sire"
                    placeholder="Enter sire"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Dam (Mother)</label>
                  <Input
                    name="dam"
                    placeholder="Enter dam"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Purchase Price</label>
                  <Input
                    type="number"
                    step="0.01"
                    name="purchasePrice"
                    placeholder="Enter purchase price"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 h-full pt-6">
                    <input
                      type="checkbox"
                      id="isRegistered"
                      name="isRegistered"
                      onChange={(e) => setIsRegistered(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                    />
                    <label htmlFor="isRegistered" className="text-sm font-medium">
                      Is Registered
                    </label>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Registration Number</label>
                  <Input
                    name="registrationNumber"
                    disabled={!isRegistered}
                    placeholder="Enter registration number"
                    className="focus:ring-2 focus:ring-emerald-500"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  name="notes"
                  rows={4}
                  placeholder="Enter any additional notes"
                  className="w-full focus:ring-2 focus:ring-emerald-500"
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  className="border-emerald-500 text-emerald-600 hover:bg-emerald-50"
                >
                  Cancel
                </Button>
                <SubmitButton />
              </div>
            </form>
          </CardContent>
          <CardFooter className="bg-gradient-to-r from-emerald-50 to-teal-50 border-t border-emerald-100 flex justify-between items-center">
            <p className="text-xs text-emerald-700">* Required fields</p>
            <p className="text-xs text-emerald-700">Your sheep will be added to the registry immediately</p>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  )
}
