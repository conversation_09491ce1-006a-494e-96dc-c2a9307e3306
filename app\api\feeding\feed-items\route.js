import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// Helper function to generate a unique feed item ID (shorter version)
function generateFeedItemId() {
  // Use a shorter timestamp (last 6 digits) and smaller random number
  const timestamp = Date.now() % 1000000; // Last 6 digits of timestamp
  const randomPart = Math.floor(Math.random() * 1000); // 3-digit random number
  return `FEED-${timestamp}${randomPart}`;
}

// Check if feed_items table exists, create it if it doesn't
async function ensureTableExists() {
  try {
    // Check if table exists
    const [tables] = await pool.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'feed_items'
    `);

    if (tables.length === 0) {
      console.log('Creating feed_items table...');

      // Create the table with a longer ID field (varchar(50) instead of varchar(20))
      await pool.execute(`
        CREATE TABLE IF NOT EXISTS feed_items (
          id varchar(50) NOT NULL,
          name varchar(100) NOT NULL,
          category varchar(50) NOT NULL,
          description text,
          quantity decimal(10,3) NOT NULL DEFAULT '0.000',
          unit varchar(20) NOT NULL,
          min_level decimal(10,3) DEFAULT NULL,
          max_level decimal(10,3) DEFAULT NULL,
          location varchar(100) DEFAULT NULL,
          expiry_date date DEFAULT NULL,
          purchase_date date DEFAULT NULL,
          price_per_unit decimal(10,2) DEFAULT NULL,
          supplier varchar(100) DEFAULT NULL,
          protein decimal(5,2) DEFAULT NULL,
          fiber decimal(5,2) DEFAULT NULL,
          fat decimal(5,2) DEFAULT NULL,
          moisture decimal(5,2) DEFAULT NULL,
          notes text,
          created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY name (name)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
      `);

      console.log('feed_items table created successfully');
    } else {
      console.log('feed_items table already exists');

      // Check if we need to alter the id column length
      const [columns] = await pool.execute(`
        SELECT COLUMN_NAME, CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'feed_items'
        AND COLUMN_NAME = 'id'
      `);

      if (columns.length > 0) {
        const idColumn = columns[0];
        console.log('Current id column length:', idColumn.CHARACTER_MAXIMUM_LENGTH);

        // If the id column is too short, alter it
        if (idColumn.CHARACTER_MAXIMUM_LENGTH < 50) {
          console.log('Altering id column to increase length...');
          await pool.execute(`
            ALTER TABLE feed_items
            MODIFY COLUMN id varchar(50) NOT NULL
          `);
          console.log('id column length increased to 50');
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error ensuring table exists:', error);
    return false;
  }
}

export async function POST(request) {
  console.log('=== FEED ITEM API POST REQUEST RECEIVED ===');

  // Check if pool is initialized
  if (!pool) {
    console.error('Database pool is not initialized');
    return NextResponse.json(
      { error: 'Database connection not available' },
      { status: 500 }
    );
  }

  // Ensure the table exists
  const tableExists = await ensureTableExists();
  if (!tableExists) {
    return NextResponse.json(
      { error: 'Failed to ensure database table exists' },
      { status: 500 }
    );
  }

  try {
    const data = await request.json();
    console.log('Received feed item data:', data);

    // Validate required fields
    if (!data.name || !data.category || !data.unit) {
      return NextResponse.json(
        { error: 'Missing required fields: name, category, and unit are required' },
        { status: 400 }
      );
    }

    // Generate a unique ID for the feed item
    const feedId = generateFeedItemId();
    console.log('Generated feed ID:', feedId);

    // Process the data
    const processedData = {
      id: feedId,
      name: data.name,
      category: data.category,
      description: data.description || null,
      quantity: data.quantity ? parseFloat(data.quantity) : 0,
      unit: data.unit,
      min_level: data.minLevel ? parseFloat(data.minLevel) : null,
      max_level: data.maxLevel ? parseFloat(data.maxLevel) : null,
      location: data.location || null,
      expiry_date: data.expiryDate || null,
      purchase_date: data.purchaseDate || null,
      price_per_unit: data.price ? parseFloat(data.price) : null,
      supplier: data.supplier || null,
      protein: data.protein ? parseFloat(data.protein) : null,
      fiber: data.fiber ? parseFloat(data.fiber) : null,
      fat: data.fat ? parseFloat(data.fat) : null,
      moisture: data.moisture ? parseFloat(data.moisture) : null,
      notes: data.notes || null
    };

    console.log('Processed data for database insertion:', processedData);

    // Prepare the SQL parameters
    const sqlParams = [
      processedData.id,
      processedData.name,
      processedData.category,
      processedData.description,
      processedData.quantity,
      processedData.unit,
      processedData.min_level,
      processedData.max_level,
      processedData.location,
      processedData.expiry_date,
      processedData.purchase_date,
      processedData.price_per_unit,
      processedData.supplier,
      processedData.protein,
      processedData.fiber,
      processedData.fat,
      processedData.moisture,
      processedData.notes
    ];

    console.log('SQL parameters:', sqlParams);

    // Insert data into the feed_items table
    const [result] = await pool.execute(
      `INSERT INTO feed_items (
        id,
        name,
        category,
        description,
        quantity,
        unit,
        min_level,
        max_level,
        location,
        expiry_date,
        purchase_date,
        price_per_unit,
        supplier,
        protein,
        fiber,
        fat,
        moisture,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      sqlParams
    );

    console.log('Database insert result:', result);

    return NextResponse.json({
      success: true,
      message: 'Feed item created successfully',
      id: feedId
    });

  } catch (error) {
    console.error('Error creating feed item:', error);

    // Check for duplicate entry error
    if (error.code === 'ER_DUP_ENTRY') {
      return NextResponse.json(
        { error: 'A feed item with this name already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: `Failed to create feed item: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Ensure the table exists
    await ensureTableExists();

    // Query to get all feed items
    const [rows] = await pool.execute(`
      SELECT * FROM feed_items
      ORDER BY name ASC
    `);

    // Calculate inventory status for each item
    const formattedItems = rows.map(item => {
      let status = 'normal';
      let percentFull = 100;

      // Calculate percentage if min and max levels are set
      if (item.min_level !== null && item.max_level !== null) {
        const range = item.max_level - item.min_level;
        if (range > 0) {
          const current = item.quantity - item.min_level;
          percentFull = Math.min(100, Math.max(0, (current / range) * 100));
        }

        // Determine status based on quantity
        if (item.quantity <= item.min_level) {
          status = 'low';
        } else if (item.quantity >= item.max_level) {
          status = 'full';
        }
      }

      return {
        ...item,
        status,
        percentFull: Math.round(percentFull)
      };
    });

    // Group items by category
    const groupedItems = formattedItems.reduce((acc, item) => {
      const category = item.category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {});

    // Get unique categories from database
    const [categories] = await pool.execute(`
      SELECT DISTINCT category, COUNT(*) as count
      FROM feed_items
      GROUP BY category
      ORDER BY category ASC
    `);

    return NextResponse.json({
      items: formattedItems,
      groupedItems,
      categories: categories
    });
  } catch (error) {
    console.error('Error fetching feed items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feed items: ' + error.message },
      { status: 500 }
    );
  }
}
