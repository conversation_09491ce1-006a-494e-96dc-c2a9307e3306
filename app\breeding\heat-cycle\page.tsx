"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { LucideArrowLeft, LucideCalendarClock, LucideSave, LucideAlertCircle } from "lucide-react";
import DashboardLayout from "@/components/dashboard-layout";
import { LoadingButton } from "@/components/ui/loading-button";

// Heat intensity options
const heatIntensityOptions = [
  { value: "mild", label: "Mild - Subtle signs" },
  { value: "moderate", label: "Moderate - Clear signs" },
  { value: "strong", label: "Strong - Very obvious signs" },
];

export default function RecordHeatCyclePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [femaleGoats, setFemaleGoats] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    goatId: "",
    detectionDate: new Date().toISOString().split("T")[0],
    heatIntensity: "moderate",
    signs: {
      tailWagging: false,
      vocalization: false,
      restlessness: false,
      mountingOthers: false,
      standingHeat: false,
      discharges: false,
      decreasedAppetite: false,
      swollenVulva: false,
    },
    notes: "",
    scheduleBreeding: false,
    breedingDate: "",
  });

  // Calculate age from birth_date
  const calculateAge = (birthDate: string | null | undefined): string => {
    if (!birthDate) return "Unknown";

    const birth = new Date(birthDate);
    const now = new Date();

    let years = now.getFullYear() - birth.getFullYear();
    const monthDiff = now.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
      years--;
    }

    return years === 1 ? "1 year" : `${years} years`;
  };

  // Fetch female goats from the database
  useEffect(() => {
    const fetchFemaleGoats = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching female goats...');

        const response = await fetch('/api/goats?gender=Female');

        if (!response.ok) {
          throw new Error(`Failed to fetch female goats: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();
        console.log('Fetched goats data:', responseData);

        // Extract the goats array from the response
        const goatsData = responseData.goats || [];

        // Transform the data to match the expected format
        const formattedGoats = goatsData.map(goat => ({
          id: goat.id.toString(),
          name: goat.name || 'Unnamed',
          breed: goat.breed || 'Unknown',
          tagNumber: goat.tag_number || goat.tag || 'No tag',
          age: calculateAge(goat.birth_date),
          status: goat.status || 'Open'
        }));

        console.log('Formatted goats:', formattedGoats);
        setFemaleGoats(formattedGoats);
      } catch (error) {
        console.error('Error fetching female goats:', error);
        // Set empty array if fetch fails
        setFemaleGoats([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFemaleGoats();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSignChange = (sign: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      signs: {
        ...prev.signs,
        [sign]: checked,
      },
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Calculate suggested breeding date (24-48 hours after heat detection)
  const calculateSuggestedBreedingDate = () => {
    if (!formData.detectionDate) return "";

    const detectionDate = new Date(formData.detectionDate);
    const suggestedDate = new Date(detectionDate);
    suggestedDate.setDate(detectionDate.getDate() + 1); // Add 24 hours (1 day)

    return suggestedDate.toISOString().split("T")[0];
  };

  // Update breeding date when detection date changes
  const handleDetectionDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const detectionDate = e.target.value;
    setFormData((prev) => ({
      ...prev,
      detectionDate,
      breedingDate: prev.scheduleBreeding ? calculateSuggestedBreedingDate() : prev.breedingDate,
    }));
  };

  // Toggle schedule breeding and set suggested date
  const handleScheduleBreedingChange = (checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      scheduleBreeding: checked,
      breedingDate: checked ? calculateSuggestedBreedingDate() : "",
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare data for API
      const heatCycleData = {
        goat_id: formData.goatId,
        heat_date: formData.detectionDate,
        intensity: formData.heatIntensity,
        signs: Object.entries(formData.signs)
          .filter(([_, value]) => value === true)
          .map(([key]) => key)
          .join(','),
        notes: formData.notes,
        breeding_scheduled: formData.scheduleBreeding ? 1 : 0,
        planned_breeding_date: formData.scheduleBreeding ? formData.breedingDate : null
      };

      console.log('Submitting heat cycle data:', heatCycleData);

      // Send data to API
      const response = await fetch('/api/breeding/heat-cycles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(heatCycleData),
      });

      const data = await response.json();
      console.log('API response:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to record heat cycle');
      }

      // Success - redirect to breeding page
      router.push("/breeding");
    } catch (error: any) {
      console.error('Error recording heat cycle:', error);
      alert(`Failed to record heat cycle: ${error.message}`);
      // You could add toast notification here
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Record Heat Cycle</h1>
          <Link href="/breeding">
            <Button variant="outline" className="btn-outline-accent">
              <LucideArrowLeft className="mr-2 h-4 w-4" />
              Back to Breeding
            </Button>
          </Link>
        </div>

        {/* Form */}
        <Card className="border-t-4 border-t-pink-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <LucideCalendarClock className="h-6 w-6 text-pink-500" />
              <CardTitle>Heat Cycle Detection</CardTitle>
            </div>
            <CardDescription>Record when a doe is in heat for breeding planning</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Goat Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-pink-700">Doe Information</h3>
                <div className="space-y-2">
                  <Label htmlFor="goatId">
                    Select Doe <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.goatId}
                    onValueChange={(value) => {
                      console.log('Selected goat ID:', value);
                      handleSelectChange("goatId", value);
                    }}
                    required
                  >
                    <SelectTrigger id="goatId" className="input-primary">
                      <SelectValue placeholder="Select a female goat" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoading ? (
                        <SelectItem value="" disabled>Loading goats...</SelectItem>
                      ) : femaleGoats.length > 0 ? (
                        femaleGoats.map((goat) => (
                          <SelectItem key={goat.id} value={goat.id}>
                            {goat.name} (Tag: {goat.tagNumber}, {goat.breed}, {goat.age})
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>No female goats found</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <div className="text-xs text-muted-foreground mt-1">
                    {isLoading ? 'Loading...' :
                     femaleGoats.length === 0 ? 'No female goats available. Please add female goats first.' :
                     `${femaleGoats.length} female goats available`}
                  </div>
                </div>
              </div>

              {/* Heat Detection Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-pink-700">Heat Detection</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="detectionDate">
                      Detection Date <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="detectionDate"
                        name="detectionDate"
                        type="date"
                        value={formData.detectionDate}
                        onChange={handleDetectionDateChange}
                        required
                        className="input-primary pl-10"
                      />
                      <LucideCalendarClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-pink-500" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="heatIntensity">
                      Heat Intensity <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.heatIntensity}
                      onValueChange={(value) => handleSelectChange("heatIntensity", value)}
                      required
                    >
                      <SelectTrigger id="heatIntensity" className="input-primary">
                        <SelectValue placeholder="Select intensity" />
                      </SelectTrigger>
                      <SelectContent>
                        {heatIntensityOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Heat Signs */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-pink-700">Observed Signs</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 border rounded-md p-4 bg-pink-50/30">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="tailWagging"
                      checked={formData.signs.tailWagging}
                      onCheckedChange={(checked) => handleSignChange("tailWagging", checked === true)}
                    />
                    <Label htmlFor="tailWagging" className="cursor-pointer">
                      Tail Wagging
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="vocalization"
                      checked={formData.signs.vocalization}
                      onCheckedChange={(checked) => handleSignChange("vocalization", checked === true)}
                    />
                    <Label htmlFor="vocalization" className="cursor-pointer">
                      Increased Vocalization
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="restlessness"
                      checked={formData.signs.restlessness}
                      onCheckedChange={(checked) => handleSignChange("restlessness", checked === true)}
                    />
                    <Label htmlFor="restlessness" className="cursor-pointer">
                      Restlessness
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="mountingOthers"
                      checked={formData.signs.mountingOthers}
                      onCheckedChange={(checked) => handleSignChange("mountingOthers", checked === true)}
                    />
                    <Label htmlFor="mountingOthers" className="cursor-pointer">
                      Mounting Others
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="standingHeat"
                      checked={formData.signs.standingHeat}
                      onCheckedChange={(checked) => handleSignChange("standingHeat", checked === true)}
                    />
                    <Label htmlFor="standingHeat" className="cursor-pointer">
                      Standing Heat
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="discharges"
                      checked={formData.signs.discharges}
                      onCheckedChange={(checked) => handleSignChange("discharges", checked === true)}
                    />
                    <Label htmlFor="discharges" className="cursor-pointer">
                      Vaginal Discharge
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="decreasedAppetite"
                      checked={formData.signs.decreasedAppetite}
                      onCheckedChange={(checked) => handleSignChange("decreasedAppetite", checked === true)}
                    />
                    <Label htmlFor="decreasedAppetite" className="cursor-pointer">
                      Decreased Appetite
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="swollenVulva"
                      checked={formData.signs.swollenVulva}
                      onCheckedChange={(checked) => handleSignChange("swollenVulva", checked === true)}
                    />
                    <Label htmlFor="swollenVulva" className="cursor-pointer">
                      Swollen Vulva
                    </Label>
                  </div>
                </div>
              </div>

              {/* Schedule Breeding */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="scheduleBreeding"
                    checked={formData.scheduleBreeding}
                    onCheckedChange={handleScheduleBreedingChange}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor="scheduleBreeding" className="cursor-pointer">
                      Schedule Breeding
                    </Label>
                    <p className="text-sm text-muted-foreground">Plan a breeding date based on this heat detection</p>
                  </div>
                </div>

                {formData.scheduleBreeding && (
                  <div className="pl-6 border-l-2 border-pink-200 space-y-4">
                    <div className="bg-pink-50 p-4 rounded-md border border-pink-200 flex gap-3">
                      <LucideAlertCircle className="h-5 w-5 text-pink-600 flex-shrink-0 mt-0.5" />
                      <div className="text-sm text-pink-800">
                        The optimal time for breeding is typically 24-48 hours after the first signs of heat are
                        detected. A suggested date has been calculated for you.
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="breedingDate">
                        Planned Breeding Date <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input
                          id="breedingDate"
                          name="breedingDate"
                          type="date"
                          value={formData.breedingDate}
                          onChange={handleChange}
                          required={formData.scheduleBreeding}
                          className="input-primary pl-10"
                        />
                        <LucideCalendarClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-pink-500" />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Notes */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-pink-700">Additional Notes</h3>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional observations or notes"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-primary min-h-[100px]"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/breeding")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Record Heat Cycle
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </div>
    </DashboardLayout>
  );
}


