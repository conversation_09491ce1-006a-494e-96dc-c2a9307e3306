"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LucideCalendarClock, LucideSave, LucideInfo } from "lucide-react"
import { BackButton } from "@/components/ui/back-button"
import DashboardLayout from "@/components/dashboard-layout"

// Breeding methods
const breedingMethods = [
  { value: "natural", label: "Natural Breeding" },
  { value: "ai", label: "Artificial Insemination" },
  { value: "embryo", label: "Embryo Transfer" },
]

export default function AddBreedingPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [femaleGoats, setFemaleGoats] = useState<any[]>([])
  const [maleGoats, setMaleGoats] = useState<any[]>([])
  const [isLoadingFemales, setIsLoadingFemales] = useState(true)
  const [isLoadingMales, setIsLoadingMales] = useState(true)
  const [formData, setFormData] = useState({
    doeId: "",
    buckId: "",
    breedingMethod: "natural",
    breedingDate: new Date().toISOString().split("T")[0],
    expectedKiddingDate: "",
    actualKiddingDate: "",
    numberOfKids: "",
    notes: "",
  })

  // Calculate age from birth_date
  const calculateAge = (birthDate) => {
    if (!birthDate) return "Unknown"

    const birth = new Date(birthDate)
    const now = new Date()

    let years = now.getFullYear() - birth.getFullYear()
    const monthDiff = now.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
      years--
    }

    return years === 1 ? "1 year" : `${years} years`
  }

  // Fetch female goats from the database
  useEffect(() => {
    const fetchFemaleGoats = async () => {
      try {
        setIsLoadingFemales(true)
        console.log('Fetching female goats...')

        const response = await fetch('/api/goats?gender=Female', {
          cache: 'no-store'
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch female goats: ${response.status} ${response.statusText}`)
        }

        const responseData = await response.json()
        console.log('Female goats response data:', responseData)

        // Extract the goats array from the response
        const goatsData = responseData.goats || []

        // Apply client-side filtering to ensure only females are included
        const femalesOnly = goatsData.filter(goat => goat.gender === 'Female')
        console.log(`Filtered to ${femalesOnly.length} female goats out of ${goatsData.length} total`)

        // Transform the data to match the expected format
        const formattedGoats = femalesOnly.map(goat => ({
          id: goat.id.toString(),
          name: goat.name || 'Unnamed',
          breed: goat.breed || 'Unknown',
          tagNumber: goat.tag_number || goat.tag || 'No tag',
          age: calculateAge(goat.birth_date),
          status: goat.status || 'Open'
        }))

        setFemaleGoats(formattedGoats)
      } catch (error) {
        console.error('Error fetching female goats:', error)
        setFemaleGoats([])
      } finally {
        setIsLoadingFemales(false)
      }
    }

    fetchFemaleGoats()
  }, [])

  // Fetch male goats from the database
  useEffect(() => {
    const fetchMaleGoats = async () => {
      try {
        setIsLoadingMales(true)
        console.log('Fetching male goats...')

        const response = await fetch('/api/goats?gender=Male', {
          cache: 'no-store'
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch male goats: ${response.status} ${response.statusText}`)
        }

        const responseData = await response.json()
        console.log('Male goats response data:', responseData)

        // Extract the goats array from the response
        const goatsData = responseData.goats || []

        // Apply client-side filtering to ensure only males are included
        const malesOnly = goatsData.filter(goat => goat.gender === 'Male')
        console.log(`Filtered to ${malesOnly.length} male goats out of ${goatsData.length} total`)

        // Transform the data to match the expected format
        const formattedGoats = malesOnly.map(goat => ({
          id: goat.id.toString(),
          name: goat.name || 'Unnamed',
          breed: goat.breed || 'Unknown',
          tagNumber: goat.tag_number || goat.tag || 'No tag',
          age: calculateAge(goat.birth_date)
        }))

        setMaleGoats(formattedGoats)
      } catch (error) {
        console.error('Error fetching male goats:', error)
        setMaleGoats([])
      } finally {
        setIsLoadingMales(false)
      }
    }

    fetchMaleGoats()
  }, [])

  // Calculate expected kidding date (approximately 150 days after breeding)
  useEffect(() => {
    if (formData.breedingDate) {
      const breedingDate = new Date(formData.breedingDate)
      const kiddingDate = new Date(breedingDate)
      kiddingDate.setDate(breedingDate.getDate() + 150) // Goat gestation is ~150 days

      setFormData((prev) => ({
        ...prev,
        expectedKiddingDate: kiddingDate.toISOString().split("T")[0],
      }))
    }
  }, [formData.breedingDate])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare the data to be sent to the API
      const breedingData = {
        doe_id: parseInt(formData.doeId),
        buck_id: formData.breedingMethod === "natural" ? parseInt(formData.buckId) : null,
        breeding_method: formData.breedingMethod,
        breeding_date: formData.breedingDate,
        expected_kidding_date: formData.expectedKiddingDate,
        actual_kidding_date: formData.actualKiddingDate || null,
        number_of_kids: formData.numberOfKids ? parseInt(formData.numberOfKids) : null,
        notes: formData.notes,
      }

      // Send the data to the API
      const response = await fetch('/api/breeding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(breedingData),
      })

      if (!response.ok) {
        throw new Error(`Failed to create breeding record: ${response.status} ${response.statusText}`)
      }

      // Redirect to the breeding page on success
      router.push('/breeding')
    } catch (error) {
      console.error('Error creating breeding record:', error)
      // Handle error (you could set an error state and display it to the user)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Add Breeding Record</h1>
          <BackButton href="/breeding" label="Back to Breeding" variant="outline" className="btn-outline-accent" />
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Breeding Information</CardTitle>
              <CardDescription>
                Record a new breeding event for your goats
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Goat Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-rose-700">Goat Selection</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="doeId">
                      Select Doe <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.doeId}
                      onValueChange={(value) => handleSelectChange("doeId", value)}
                      required
                    >
                      <SelectTrigger id="doeId" className="input-primary">
                        <SelectValue placeholder="Select a female goat" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingFemales ? (
                          <SelectItem value="" disabled>Loading does...</SelectItem>
                        ) : femaleGoats.length > 0 ? (
                          femaleGoats.map((goat) => (
                            <SelectItem key={goat.id} value={goat.id}>
                              {goat.name} (Tag: {goat.tagNumber}, {goat.breed}, {goat.age})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="" disabled>No female goats found</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <div className="text-xs text-muted-foreground mt-1">
                      {isLoadingFemales ? 'Loading...' :
                       femaleGoats.length === 0 ? 'No female goats available. Please add female goats first.' :
                       `${femaleGoats.length} female goats available`}
                    </div>
                  </div>

                  {formData.breedingMethod === "natural" && (
                    <div className="space-y-2">
                      <Label htmlFor="buckId">
                        Select Buck <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={formData.buckId}
                        onValueChange={(value) => handleSelectChange("buckId", value)}
                        required={formData.breedingMethod === "natural"}
                      >
                        <SelectTrigger id="buckId" className="input-primary">
                          <SelectValue placeholder="Select a male goat" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingMales ? (
                            <SelectItem value="" disabled>Loading bucks...</SelectItem>
                          ) : maleGoats.length > 0 ? (
                            maleGoats.map((goat) => (
                              <SelectItem key={goat.id} value={goat.id}>
                                {goat.name} (Tag: {goat.tagNumber}, {goat.breed}, {goat.age})
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No male goats found</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <div className="text-xs text-muted-foreground mt-1">
                        {isLoadingMales ? 'Loading...' :
                         maleGoats.length === 0 ? 'No male goats available. Please add male goats first.' :
                         `${maleGoats.length} male goats available`}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Breeding Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-rose-700">Breeding Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="breedingMethod">
                      Breeding Method <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.breedingMethod}
                      onValueChange={(value) => handleSelectChange("breedingMethod", value)}
                      required
                    >
                      <SelectTrigger id="breedingMethod" className="input-primary">
                        <SelectValue placeholder="Select breeding method" />
                      </SelectTrigger>
                      <SelectContent>
                        {breedingMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="breedingDate">
                      Breeding Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      type="date"
                      id="breedingDate"
                      name="breedingDate"
                      value={formData.breedingDate}
                      onChange={handleChange}
                      className="input-primary"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expectedKiddingDate">
                      Expected Kidding Date <LucideCalendarClock className="inline h-4 w-4 ml-1" />
                    </Label>
                    <Input
                      type="date"
                      id="expectedKiddingDate"
                      name="expectedKiddingDate"
                      value={formData.expectedKiddingDate}
                      onChange={handleChange}
                      className="input-primary"
                      readOnly
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      Automatically calculated (150 days after breeding date)
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className="input-primary min-h-[100px]"
                  placeholder="Add any additional notes about this breeding..."
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={() => router.push("/breeding")}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="btn-primary">
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>
                    <LucideSave className="mr-2 h-4 w-4" />
                    Save Breeding Record
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </div>
    </DashboardLayout>
  )
}








