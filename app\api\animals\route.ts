import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const animalType = searchParams.get('type');
    const gender = searchParams.get('gender');
    const status = searchParams.get('status');

    console.log('GET /api/animals - Filters:', { animalType, gender, status });

    // Query to get all animals with calculated age
    let query = `
      SELECT
        id,
        tag_number,
        name,
        animal_type,
        breed,
        gender,
        status,
        birth_date,
        weight,
        color,
        markings,
        notes,
        CASE
          WHEN birth_date IS NOT NULL THEN
            CONCAT(
              FLOOR(DATEDIFF(CURDATE(), birth_date) / 365),
              ' years, ',
              FLOOR((DATEDIFF(CURDATE(), birth_date) % 365) / 30),
              ' months'
            )
          ELSE 'Unknown'
        END as age
      FROM animals
      WHERE 1=1
    `;
    let params: any[] = [];

    // Filter by animal type if specified
    if (animalType && animalType !== 'all') {
      query += ' AND animal_type = ?';
      params.push(animalType);
    }

    // Filter by gender if specified
    if (gender && gender !== 'all') {
      query += ' AND gender = ?';
      params.push(gender);
    }

    // Filter by status if specified
    if (status && status !== 'all') {
      query += ' AND status = ?';
      params.push(status);
    }

    query += ' ORDER BY name ASC';

    console.log('Executing query:', query, 'with params:', params);
    const [rows] = await pool.execute(query, params);
    console.log(`Found ${(rows as any[]).length} animals matching criteria`);

    // Calculate statistics by animal type
    const animals = rows as any[];
    const stats = {
      total: animals.length,
      byType: {
        Goat: animals.filter(a => a.animal_type === 'Goat').length,
        Sheep: animals.filter(a => a.animal_type === 'Sheep').length,
        Cattle: animals.filter(a => a.animal_type === 'Cattle').length,
        Pig: animals.filter(a => a.animal_type === 'Pig').length,
      },
      byStatus: {
        healthy: animals.filter(a => a.status === 'Healthy').length,
        sick: animals.filter(a => a.status === 'Sick').length,
        injured: animals.filter(a => a.status === 'Injured').length,
        quarantined: animals.filter(a => a.status === 'Quarantined').length,
        pregnant: animals.filter(a => a.status === 'Pregnant').length,
        lactating: animals.filter(a => a.status === 'Lactating').length,
        deceased: animals.filter(a => a.status === 'Deceased').length,
      },
      byGender: {
        males: animals.filter(a => a.gender === 'Male').length,
        females: animals.filter(a => a.gender === 'Female').length,
      }
    };

    console.log('Calculated stats:', stats);

    // Return both animals and stats
    return NextResponse.json({
      animals: animals,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching animals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch animals' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    const {
      tagNumber,
      name,
      animalType,
      breed,
      gender,
      birthDate,
      acquisitionDate,
      status = 'Healthy',
      weight,
      color,
      markings,
      sire,
      dam,
      purchasePrice,
      isRegistered,
      registrationNumber,
      notes,
    } = body;

    // Validate required fields
    if (!tagNumber || !name || !animalType || !breed || !gender) {
      return NextResponse.json(
        { error: 'Missing required fields: tagNumber, name, animalType, breed, gender' },
        { status: 400 }
      );
    }

    // Validate animal type
    const validAnimalTypes = ['Goat', 'Sheep', 'Cattle', 'Pig'];
    if (!validAnimalTypes.includes(animalType)) {
      return NextResponse.json(
        { error: 'Invalid animal type. Must be one of: ' + validAnimalTypes.join(', ') },
        { status: 400 }
      );
    }

    const [result] = await pool.execute(
      `INSERT INTO animals (
        tag_number,
        name,
        animal_type,
        breed,
        gender,
        birth_date,
        acquisition_date,
        status,
        weight,
        color,
        markings,
        sire,
        dam,
        purchase_price,
        is_registered,
        registration_number,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tagNumber,
        name,
        animalType,
        breed,
        gender,
        birthDate || null,
        acquisitionDate || null,
        status,
        weight || null,
        color || null,
        markings || null,
        sire || null,
        dam || null,
        purchasePrice || null,
        isRegistered ? 1 : 0,
        registrationNumber || null,
        notes || null,
      ]
    );

    return NextResponse.json({
      message: `${animalType} added successfully`,
      animalId: (result as any).insertId
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error adding animal:', error);
    
    // Handle duplicate tag number error
    if (error.code === 'ER_DUP_ENTRY') {
      return NextResponse.json(
        { error: 'Tag number already exists. Please use a unique tag number.' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to add animal' },
      { status: 500 }
    );
  }
}
