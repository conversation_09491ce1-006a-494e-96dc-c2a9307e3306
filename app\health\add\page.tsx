"use client"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { DatePicker } from "@/components/ui/date-picker"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucideHeart, LucideSave, LucideCalendarClock, LucideAlertCircle, LucideRefreshCw } from "lucide-react"

import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

export default function AddHealthRecordPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [goats, setGoats] = useState([])
  const [isLoadingGoats, setIsLoadingGoats] = useState(true)
  const [formData, setFormData] = useState({
    goat_id: "",
    record_date: new Date().toISOString().split("T")[0],
    record_type: "deworming", // Using deworming as the default
    diagnosis: "",
    treatment: "",
    medication: "",
    dosage: "",
    administered_by: "",
    vet_name: "",
    follow_up_date: "",
    outcome: "ongoing",
    notes: "",
    is_critical: false
  })

  // Function to fetch goats from the database
  const fetchGoats = async () => {
    setIsLoadingGoats(true)
    try {
      const response = await fetch('/api/goats')
      if (response.ok) {
        const data = await response.json()
        // The API returns an object with a goats property that contains the array of goats
        setGoats(data.goats || [])
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch goats. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error fetching goats:', error)
      toast({
        title: "Error",
        description: "Failed to fetch goats. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingGoats(false)
    }
  }

  // Fetch goats when component mounts
  useEffect(() => {
    fetchGoats()
  }, [])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleDateChange = (field, date) => {
    setFormData((prev) => ({ ...prev, [field]: date }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Submit the form data
      const response = await fetch('/api/health-records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      // Simple response handling
      if (response.ok) {
        // Success case
        toast({
          title: "Success",
          description: "Health record added successfully",
        })
        router.push("/health")
      } else {
        // Error case - simple message
        toast({
          title: "Error",
          description: "Failed to add health record. Please check your inputs and try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      // Network error case
      toast({
        title: "Error",
        description: "Network error or server unavailable. Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-red">Add Health Record</h1>
          <p className="text-muted-foreground">
            Create a new health record for a goat in your herd
          </p>
        </div>
        <BackButton href="/health" label="Back to Health Records" />
      </div>

      {/* Form */}
      <Card className="border-t-4 border-t-red-500">
        <CardHeader>
          <div className="flex items-center gap-2">
            <LucideHeart className="h-6 w-6 text-red-500" />
            <CardTitle>Health Record Information</CardTitle>
          </div>
          <CardDescription>Document health issues, treatments, and outcomes for your goats.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Goat Selection */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium text-red-700">Goat Selection</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="goat_id">
                      Select Goat <span className="text-red-500">*</span>
                    </Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={fetchGoats}
                      disabled={isLoadingGoats}
                      className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <LucideRefreshCw className={`h-4 w-4 mr-1 ${isLoadingGoats ? 'animate-spin' : ''}`} />
                      {isLoadingGoats ? 'Loading...' : 'Refresh'}
                    </Button>
                  </div>
                  <Select
                    value={formData.goat_id}
                    onValueChange={(value) => handleSelectChange("goat_id", value)}
                  >
                    <SelectTrigger className="input-primary">
                      <SelectValue placeholder="Select a goat" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingGoats ? (
                        <SelectItem value="loading" disabled>
                          Loading goats...
                        </SelectItem>
                      ) : goats.length > 0 ? (
                        goats.map((goat) => (
                          <SelectItem key={goat.id} value={goat.id.toString()}>
                            {goat.name} ({goat.tag || 'No tag'}) - {goat.breed}, {goat.gender}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-goats" disabled>
                          No goats found
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {goats.length > 0 && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {goats.length} goat{goats.length !== 1 ? 's' : ''} available
                    </p>
                  )}
                </div>
              </div>

              {/* Health Record Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-red-700">Health Record Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="record_date">
                      Record Date <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="record_date"
                        name="record_date"
                        type="date"
                        value={formData.record_date}
                        onChange={handleChange}
                        required
                        className="input-primary pl-10"
                      />
                      <LucideCalendarClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-red-500" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="record_type">
                      Record Type <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.record_type}
                      onValueChange={(value) => handleSelectChange("record_type", value)}
                    >
                      <SelectTrigger className="input-primary">
                        <SelectValue placeholder="Select record type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Vaccination">Vaccination</SelectItem>
                        <SelectItem value="Medication">Medication</SelectItem>
                        <SelectItem value="Illness">Illness</SelectItem>
                        <SelectItem value="Injury">Injury</SelectItem>
                        <SelectItem value="Checkup">Checkup</SelectItem>
                        {/* Additional options - these will be mapped to "Other" in the API */}
                        <SelectItem value="deworming">Deworming</SelectItem>
                        <SelectItem value="hoof_trimming">Hoof Trimming</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="diagnosis">
                    Diagnosis/Issue <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="diagnosis"
                    name="diagnosis"
                    placeholder="Describe the health issue or diagnosis"
                    value={formData.diagnosis}
                    onChange={handleChange}
                    required
                    className="input-primary min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="treatment">
                    Treatment
                  </Label>
                  <Textarea
                    id="treatment"
                    name="treatment"
                    placeholder="Describe the treatment provided"
                    value={formData.treatment}
                    onChange={handleChange}
                    className="input-primary min-h-[80px]"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="medication">
                      Medication
                    </Label>
                    <Input
                      id="medication"
                      name="medication"
                      placeholder="Medication name"
                      value={formData.medication}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dosage">
                      Dosage
                    </Label>
                    <Input
                      id="dosage"
                      name="dosage"
                      placeholder="Medication dosage"
                      value={formData.dosage}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="administered_by">
                      Administered By
                    </Label>
                    <Input
                      id="administered_by"
                      name="administered_by"
                      placeholder="Person who administered treatment"
                      value={formData.administered_by}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vet_name">
                      Veterinarian Name
                    </Label>
                    <Input
                      id="vet_name"
                      name="vet_name"
                      placeholder="Veterinarian name (if applicable)"
                      value={formData.vet_name}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="follow_up_date">
                      Follow-up Date
                    </Label>
                    <Input
                      id="follow_up_date"
                      name="follow_up_date"
                      type="date"
                      value={formData.follow_up_date}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="outcome">
                      Outcome
                    </Label>
                    <Select
                      value={formData.outcome}
                      onValueChange={(value) => handleSelectChange("outcome", value)}
                    >
                      <SelectTrigger className="input-primary">
                        <SelectValue placeholder="Select outcome" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ongoing">Ongoing</SelectItem>
                        <SelectItem value="recovered">Recovered</SelectItem>
                        <SelectItem value="chronic">Chronic</SelectItem>
                        <SelectItem value="deceased">Deceased</SelectItem>
                        <SelectItem value="unknown">Unknown</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">
                    Additional Notes
                  </Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Any additional notes or observations"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-primary min-h-[80px]"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_critical"
                    name="is_critical"
                    checked={formData.is_critical}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({ ...prev, is_critical: checked }))
                    }
                  />
                  <Label
                    htmlFor="is_critical"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
                  >
                    <LucideAlertCircle className="h-4 w-4 text-red-500" />
                    Mark as critical condition
                  </Label>
                </div>
              </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.push("/health")}>
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Saving..."
              className="bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              <LucideSave className="mr-2 h-4 w-4" />
              Save Health Record
            </LoadingButton>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}



