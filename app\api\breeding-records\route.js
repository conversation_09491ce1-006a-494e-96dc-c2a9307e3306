import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET() {
  try {
    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Fetch all breeding records with goat names
    const [rows] = await connection.execute(`
      SELECT
        br.*,
        doe.name AS doe_name,
        doe.breed AS doe_breed,
        buck.name AS buck_name,
        buck.breed AS buck_breed
      FROM breeding_records br
      LEFT JOIN goats doe ON br.doe_id = doe.id
      LEFT JOIN goats buck ON br.buck_id = buck.id
      ORDER BY br.breeding_date DESC
    `);

    // Get breeding statistics
    const [stats] = await connection.execute(`
      SELECT
        COUNT(*) AS total_breedings,
        SUM(CASE WHEN status = 'Confirmed' THEN 1 ELSE 0 END) AS confirmed_pregnancies,
        SUM(CASE WHEN status = 'Kidded' THEN 1 ELSE 0 END) AS successful_kiddings,
        SUM(CASE WHEN status = 'Failed' THEN 1 ELSE 0 END) AS failed_breedings,
        SUM(CASE WHEN status = 'Pending' THEN 1 ELSE 0 END) AS pending_confirmations,
        AVG(CASE WHEN number_of_kids > 0 THEN number_of_kids ELSE NULL END) AS avg_kids_per_birth,
        COUNT(CASE WHEN status IN ('Pending', 'Confirmed') THEN 1 ELSE NULL END) AS active_breedings_count
      FROM breeding_records
    `);

    // Get upcoming kiddings
    const [upcomingKiddings] = await connection.execute(`
      SELECT
        br.*,
        g.name AS doe_name,
        g.breed AS doe_breed
      FROM breeding_records br
      JOIN goats g ON br.doe_id = g.id
      WHERE br.status = 'Confirmed'
      AND br.expected_kidding_date IS NOT NULL
      AND br.expected_kidding_date >= CURDATE()
      ORDER BY br.expected_kidding_date ASC
      LIMIT 10
    `);

    // Get does in heat from the heat_cycles table
    const [heatCycles] = await connection.execute(`
      SELECT
        hc.*,
        g.name AS goat_name,
        g.breed AS goat_breed,
        g.tag_number AS goat_tag
      FROM heat_cycles hc
      JOIN goats g ON hc.goat_id = g.id
      ORDER BY hc.heat_date DESC
      LIMIT 10
    `);

    // Get recent heat cycles (last 30 days)
    const [recentHeatCycles] = await connection.execute(`
      SELECT
        hc.*,
        g.name AS goat_name,
        g.breed AS goat_breed,
        g.tag_number AS goat_tag
      FROM heat_cycles hc
      JOIN goats g ON hc.goat_id = g.id
      WHERE hc.heat_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      ORDER BY hc.heat_date DESC
      LIMIT 5
    `);

    // Count does currently in heat (last 3 days)
    const [doesInHeatCount] = await connection.execute(`
      SELECT COUNT(*) AS count
      FROM heat_cycles hc
      WHERE hc.heat_date >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
      AND hc.breeding_scheduled = 0
    `);

    // Calculate success rate
    let successRate = 0;
    if (stats[0].total_breedings > 0) {
      successRate = Math.round((stats[0].successful_kiddings / stats[0].total_breedings) * 100);
    }

    // Log the actual values for debugging
    console.log('Stats from database:', {
      total_breedings: stats[0].total_breedings,
      pending_confirmations: stats[0].pending_confirmations,
      confirmed_pregnancies: stats[0].confirmed_pregnancies,
      active_breedings_count: stats[0].active_breedings_count
    });

    // Format the response with proper type conversion to ensure we get numbers
    const formattedStats = {
      activeBreedings: Number(stats[0].active_breedings_count || 0),
      pregnantDoes: Number(stats[0].confirmed_pregnancies || 0),
      successRate: successRate,
      kidsPerBirth: stats[0].avg_kids_per_birth ? parseFloat(stats[0].avg_kids_per_birth).toFixed(1) : '0.0',
      upcomingKiddings: upcomingKiddings.length,
      doesInHeat: Number(doesInHeatCount[0].count || 0)
    };

    // Generate upcoming events from breeding records and heat cycles
    const upcomingEvents = [];

    // Add upcoming kiddings to events
    upcomingKiddings.forEach(kidding => {
      upcomingEvents.push({
        id: `kidding-${kidding.id}`,
        type: 'kidding',
        title: `Expected Kidding: ${kidding.doe_name}`,
        date: kidding.expected_kidding_date,
        description: `${kidding.doe_breed} doe, bred on ${new Date(kidding.breeding_date).toLocaleDateString()}`
      });
    });

    // Add scheduled breedings from heat cycles
    heatCycles.forEach(cycle => {
      if (cycle.breeding_scheduled && cycle.planned_breeding_date) {
        upcomingEvents.push({
          id: `breeding-${cycle.id}`,
          type: 'breeding',
          title: `Scheduled Breeding: ${cycle.goat_name}`,
          date: cycle.planned_breeding_date,
          description: `${cycle.intensity} heat detected on ${new Date(cycle.heat_date).toLocaleDateString()}`
        });
      }
    });

    // Add recent heat detections
    recentHeatCycles.forEach(cycle => {
      if (!cycle.breeding_scheduled) {
        upcomingEvents.push({
          id: `heat-${cycle.id}`,
          type: 'heat',
          title: `Heat Detected: ${cycle.goat_name}`,
          date: cycle.heat_date,
          description: `${cycle.intensity} signs of heat observed`
        });
      }
    });

    // Sort events by date
    upcomingEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

    await connection.end();

    return NextResponse.json({
      records: rows,
      stats: formattedStats,
      upcomingKiddings,
      heatCycles: recentHeatCycles,
      upcomingEvents
    });
  } catch (error) {
    console.error('Error fetching breeding records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch breeding records' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.doe_id || !data.breeding_date || !data.breeding_method) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Insert the breeding record
    const [result] = await connection.execute(
      `INSERT INTO breeding_records (
        doe_id, buck_id, breeding_method, breeding_date,
        expected_kidding_date, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        data.doe_id,
        data.buck_id || null,
        data.breeding_method,
        data.breeding_date,
        data.expected_kidding_date || null,
        data.notes || null,
        data.status || 'Pending'
      ]
    );

    await connection.end();

    return NextResponse.json({
      success: true,
      message: 'Breeding record added successfully',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error adding breeding record:', error);
    return NextResponse.json(
      { error: 'Failed to add breeding record' },
      { status: 500 }
    );
  }
}
