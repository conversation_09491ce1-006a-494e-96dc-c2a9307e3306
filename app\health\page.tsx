"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  LucidePlus,
  LucideCalendarClock,
  LucideHeart,
  LucideFilter,
  LucideSearch,
  LucideLoader2,
  LucideAlertTriangle,
  LucideRefreshCw
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function HealthPage() {
  const [healthRecords, setHealthRecords] = useState([])
  const [filteredRecords, setFilteredRecords] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("records")

  // Fetch health records from the API
  const fetchHealthRecords = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/health-records')

      if (!response.ok) {
        throw new Error(`Failed to fetch health records: ${response.status}`)
      }

      const data = await response.json()
      setHealthRecords(data)
      setFilteredRecords(data)
    } catch (err) {
      console.error('Error fetching health records:', err)
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchHealthRecords()
  }, [])

  // Filter records based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredRecords(healthRecords)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      const filtered = healthRecords.filter(record =>
        (record.diagnosis && record.diagnosis.toLowerCase().includes(lowercasedSearch)) ||
        (record.goat_name && record.goat_name.toLowerCase().includes(lowercasedSearch)) ||
        (record.record_type && record.record_type.toLowerCase().includes(lowercasedSearch))
      )
      setFilteredRecords(filtered)
    }
  }, [searchTerm, healthRecords])

  // Filter records based on active tab
  useEffect(() => {
    if (activeTab === "records") {
      setFilteredRecords(healthRecords)
    } else if (activeTab === "vaccinations") {
      setFilteredRecords(healthRecords.filter(record =>
        record.record_type === "Vaccination"
      ))
    } else if (activeTab === "treatments") {
      setFilteredRecords(healthRecords.filter(record =>
        record.record_type === "Medication" ||
        record.record_type === "Treatment" ||
        record.record_type === "Other"
      ))
    }
  }, [activeTab, healthRecords])

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Generate upcoming events from health records with follow-up dates
  const getUpcomingEvents = () => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return healthRecords
      .filter(record => record.follow_up_date && new Date(record.follow_up_date) >= today)
      .map(record => ({
        id: record.id,
        type: record.record_type,
        title: `Follow-up: ${record.diagnosis}`,
        date: record.follow_up_date,
        goats: record.goat_name || "Unknown",
        goat_id: record.goat_id
      }))
      .sort((a, b) => new Date(a.date) - new Date(b.date))
  }

  return (
    <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Health Records</h1>
          <div className="flex gap-2">
            <Link href="/health/add">
              <Button className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add Health Record
              </Button>
            </Link>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search health records..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
          </div>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={fetchHealthRecords}
            disabled={isLoading}
          >
            {isLoading ? (
              <LucideLoader2 className="h-4 w-4 animate-spin" />
            ) : (
              <LucideRefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>

        {/* Error state */}
        {error && !isLoading && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
            <h3 className="font-medium flex items-center gap-2">
              <LucideAlertTriangle className="h-5 w-5" />
              Error Loading Health Records
            </h3>
            <p className="mt-1 text-sm">{error}</p>
            <Button
              variant="outline"
              className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
              onClick={fetchHealthRecords}
            >
              Retry
            </Button>
          </div>
        )}

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 p-1 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50 rounded-xl">
          <TabsTrigger
            value="records"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white transition-all duration-300 hover:text-purple-700"
          >
            All Records
          </TabsTrigger>
          <TabsTrigger
            value="upcoming"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white transition-all duration-300 hover:text-green-700"
          >
            Upcoming Events
          </TabsTrigger>
          <TabsTrigger
            value="vaccinations"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-300 hover:text-indigo-700"
          >
            Vaccinations
          </TabsTrigger>
          <TabsTrigger
            value="treatments"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white transition-all duration-300 hover:text-blue-700"
          >
            Treatments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="records" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <LucideHeart className="h-5 w-5 text-purple-500" />
                  <CardTitle>Health Records</CardTitle>
                </div>
                {!isLoading && filteredRecords.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredRecords.length} of {healthRecords.length} records
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading health records...</p>
                </div>
              ) : filteredRecords.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Goat</TableHead>
                        <TableHead>Record Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Diagnosis</TableHead>
                        <TableHead>Treatment</TableHead>
                        <TableHead>Follow-up</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-medium">{record.goat_name || "Unknown"}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={
                                record.record_type === "Vaccination"
                                  ? "bg-green-100 text-green-700 hover:bg-green-200 border-green-300"
                                  : record.record_type === "Medication"
                                    ? "bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-300"
                                    : record.record_type === "Illness"
                                      ? "bg-red-100 text-red-700 hover:bg-red-200 border-red-300"
                                      : record.record_type === "Injury"
                                        ? "bg-orange-100 text-orange-700 hover:bg-orange-200 border-orange-300"
                                        : "bg-purple-100 text-purple-700 hover:bg-purple-200 border-purple-300"
                              }
                            >
                              {record.record_type}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatDate(record.record_date)}</TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.diagnosis}>
                            {record.diagnosis}
                          </TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.treatment}>
                            {record.treatment || "N/A"}
                          </TableCell>
                          <TableCell>{record.follow_up_date ? formatDate(record.follow_up_date) : "N/A"}</TableCell>
                          <TableCell>
                            <Link href={`/health/${record.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <LucideHeart className="h-4 w-4 text-purple-500" />
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucideHeart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No health records found</h3>
                  <p className="text-muted-foreground mb-4">Add your first health record to get started.</p>
                  <Link href="/health/add">
                    <Button className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Health Record
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-6 mt-6">
          {/* Upcoming health events */}
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
              <p className="text-muted-foreground">Loading upcoming events...</p>
            </div>
          ) : getUpcomingEvents().length > 0 ? (
            <div className="grid gap-6 md:grid-cols-3">
              {getUpcomingEvents().map((event) => (
                <Card
                  key={event.id}
                  className={
                    event.type === "Vaccination"
                      ? "border-l-4 border-l-green-500"
                      : event.type === "Medication"
                        ? "border-l-4 border-l-blue-500"
                        : event.type === "Illness"
                          ? "border-l-4 border-l-red-500"
                          : event.type === "Injury"
                            ? "border-l-4 border-l-orange-500"
                            : "border-l-4 border-l-purple-500"
                  }
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <Badge
                        variant="outline"
                        className={
                          event.type === "Vaccination"
                            ? "bg-green-100 text-green-700 hover:bg-green-200 border-green-300"
                            : event.type === "Medication"
                              ? "bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-300"
                              : event.type === "Illness"
                                ? "bg-red-100 text-red-700 hover:bg-red-200 border-red-300"
                                : event.type === "Injury"
                                  ? "bg-orange-100 text-orange-700 hover:bg-orange-200 border-orange-300"
                                  : "bg-purple-100 text-purple-700 hover:bg-purple-200 border-purple-300"
                        }
                      >
                        {event.type}
                      </Badge>
                      <LucideCalendarClock
                        className={
                          event.type === "Vaccination"
                            ? "h-4 w-4 text-green-500"
                            : event.type === "Medication"
                              ? "h-4 w-4 text-blue-500"
                              : event.type === "Illness"
                                ? "h-4 w-4 text-red-500"
                                : event.type === "Injury"
                                  ? "h-4 w-4 text-orange-500"
                                  : "h-4 w-4 text-purple-500"
                        }
                      />
                    </div>
                    <CardTitle className="text-lg">{event.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-muted-foreground">
                      <p>Date: {formatDate(event.date)}</p>
                      <p>Goat: {event.goats}</p>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Link href={`/health/${event.id}`} className="flex-1">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-purple-200 text-purple-700 hover:bg-purple-50 hover:border-purple-300"
                        >
                          View Details
                        </Button>
                      </Link>
                      <Link href={`/goats/${event.goat_id}`} className="flex-1">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300"
                        >
                          View Goat
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <LucideCalendarClock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-1">No upcoming events</h3>
              <p className="text-muted-foreground mb-4">
                Add health records with follow-up dates to see upcoming events here.
              </p>
              <Link href="/health/add">
                <Button className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white">
                  <LucidePlus className="mr-2 h-4 w-4" />
                  Add Health Record
                </Button>
              </Link>
            </div>
          )}
        </TabsContent>

        <TabsContent value="vaccinations" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <LucideHeart className="h-5 w-5 text-indigo-500" />
                  <CardTitle>Vaccination Records</CardTitle>
                </div>
                {!isLoading && activeTab === "vaccinations" && filteredRecords.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredRecords.length} vaccination records
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading vaccination records...</p>
                </div>
              ) : activeTab === "vaccinations" && filteredRecords.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Goat</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Diagnosis</TableHead>
                        <TableHead>Treatment</TableHead>
                        <TableHead>Follow-up</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-medium">{record.goat_name || "Unknown"}</TableCell>
                          <TableCell>{formatDate(record.record_date)}</TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.diagnosis}>
                            {record.diagnosis}
                          </TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.treatment}>
                            {record.treatment || "N/A"}
                          </TableCell>
                          <TableCell>{record.follow_up_date ? formatDate(record.follow_up_date) : "N/A"}</TableCell>
                          <TableCell>
                            <Link href={`/health/${record.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <LucideHeart className="h-4 w-4 text-indigo-500" />
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucideHeart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No vaccination records found</h3>
                  <p className="text-muted-foreground mb-4">Add vaccination records to see them here.</p>
                  <Link href="/health/add">
                    <Button className="bg-gradient-to-r from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600 text-white">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Vaccination Record
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="treatments" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <LucideHeart className="h-5 w-5 text-blue-500" />
                  <CardTitle>Treatment Records</CardTitle>
                </div>
                {!isLoading && activeTab === "treatments" && filteredRecords.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredRecords.length} treatment records
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading treatment records...</p>
                </div>
              ) : activeTab === "treatments" && filteredRecords.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Goat</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Diagnosis</TableHead>
                        <TableHead>Treatment</TableHead>
                        <TableHead>Follow-up</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-medium">{record.goat_name || "Unknown"}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={
                                record.record_type === "Medication"
                                  ? "bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-300"
                                  : record.record_type === "Illness"
                                    ? "bg-red-100 text-red-700 hover:bg-red-200 border-red-300"
                                    : record.record_type === "Injury"
                                      ? "bg-orange-100 text-orange-700 hover:bg-orange-200 border-orange-300"
                                      : "bg-purple-100 text-purple-700 hover:bg-purple-200 border-purple-300"
                              }
                            >
                              {record.record_type}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatDate(record.record_date)}</TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.diagnosis}>
                            {record.diagnosis}
                          </TableCell>
                          <TableCell className="max-w-[200px] truncate" title={record.treatment}>
                            {record.treatment || "N/A"}
                          </TableCell>
                          <TableCell>{record.follow_up_date ? formatDate(record.follow_up_date) : "N/A"}</TableCell>
                          <TableCell>
                            <Link href={`/health/${record.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <LucideHeart className="h-4 w-4 text-blue-500" />
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucideHeart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No treatment records found</h3>
                  <p className="text-muted-foreground mb-4">Add treatment records to see them here.</p>
                  <Link href="/health/add">
                    <Button className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Treatment Record
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

