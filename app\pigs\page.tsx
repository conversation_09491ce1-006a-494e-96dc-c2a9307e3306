'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LucideSearch, LucidePlus, LucideFilter, LucideUsers, LucideHeart, LucideActivity, LucideArrowLeft } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

interface Pig {
  id: number
  name: string
  breed: string
  gender: string
  status: string
  age: string
  tag: string
}

interface PigStats {
  total: number
  healthy: number
  sick: number
  injured: number
  quarantined: number
  pregnant: number
  lactating: number
  males: number
  females: number
}

export default function PigsPage() {
  const [pigs, setPigs] = useState<Pig[]>([])
  const [stats, setStats] = useState<PigStats>({
    total: 0,
    healthy: 0,
    sick: 0,
    injured: 0,
    quarantined: 0,
    pregnant: 0,
    lactating: 0,
    males: 0,
    females: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [genderFilter, setGenderFilter] = useState("all")
  const [breedFilter, setBreedFilter] = useState("all")

  useEffect(() => {
    fetchPigs()
  }, [genderFilter])

  const fetchPigs = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (genderFilter !== 'all') {
        params.append('gender', genderFilter)
      }

      const response = await fetch(`/api/pigs?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch pigs')
      }

      const data = await response.json()
      setPigs(data.pigs || [])
      setStats(data.stats || stats)
    } catch (error) {
      console.error('Error fetching pigs:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredPigs = pigs.filter(pig => {
    const matchesSearch = pig.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pig.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pig.tag.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesBreed = breedFilter === "all" || pig.breed === breedFilter

    return matchesSearch && matchesBreed
  })

  // Function to get appropriate pig image based on breed and gender
  const getPigImage = (breed: string, gender: string, name: string) => {
    const seed = `${name}-${breed}-${gender}`.toLowerCase().replace(/\s+/g, '-')
    
    // Pig breeds - different colors for different breeds
    if (breed.toLowerCase().includes('large white')) {
      // Large White pigs - pink/white
      return `https://via.placeholder.com/300x200/FFC0CB/8B4513?text=${encodeURIComponent(`${breed} ${gender} Pig`)}`
    } else if (breed.toLowerCase().includes('landrace')) {
      // Landrace pigs - white
      return `https://via.placeholder.com/300x200/FFFFFF/8B4513?text=${encodeURIComponent(`${breed} ${gender} Pig`)}`
    } else if (breed.toLowerCase().includes('duroc')) {
      // Duroc pigs - red/brown
      return `https://via.placeholder.com/300x200/CD853F/FFFFFF?text=${encodeURIComponent(`${breed} ${gender} Pig`)}`
    } else if (breed.toLowerCase().includes('yorkshire')) {
      // Yorkshire pigs - white
      return `https://via.placeholder.com/300x200/F5F5DC/8B4513?text=${encodeURIComponent(`${breed} ${gender} Pig`)}`
    } else {
      // General pigs - pink
      return `https://via.placeholder.com/300x200/FFB6C1/8B4513?text=${encodeURIComponent(`${breed} ${gender} Pig`)}`
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading pigs...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="gap-2">
                <LucideArrowLeft className="h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Pig Management</h1>
              <p className="text-muted-foreground">
                Manage your pig herd with comprehensive tracking and care records
              </p>
            </div>
          </div>
          <Link href="/pigs/add">
            <Button className="gap-2 bg-pink-600 hover:bg-pink-700">
              <LucidePlus className="h-4 w-4" />
              Add Pig
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pigs</CardTitle>
              <LucideUsers className="h-4 w-4 text-pink-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.males} boars, {stats.females} sows
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Healthy</CardTitle>
              <LucideHeart className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.healthy}</div>
              <p className="text-xs text-muted-foreground">
                {stats.total > 0 ? Math.round((stats.healthy / stats.total) * 100) : 0}% of herd
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Need Attention</CardTitle>
              <LucideActivity className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.sick + stats.injured + stats.quarantined}
              </div>
              <p className="text-xs text-muted-foreground">
                Sick, injured, or quarantined
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Breeding</CardTitle>
              <LucideHeart className="h-4 w-4 text-pink-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-pink-600">
                {stats.pregnant + stats.lactating}
              </div>
              <p className="text-xs text-muted-foreground">
                Pregnant or lactating
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LucideFilter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <LucideSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search pigs by name, breed, or tag..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={genderFilter} onValueChange={setGenderFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    <SelectItem value="Male">Boars</SelectItem>
                    <SelectItem value="Female">Sows</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={breedFilter} onValueChange={setBreedFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Breed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Breeds</SelectItem>
                    <SelectItem value="Large White">Large White</SelectItem>
                    <SelectItem value="Landrace">Landrace</SelectItem>
                    <SelectItem value="Duroc">Duroc</SelectItem>
                    <SelectItem value="Yorkshire">Yorkshire</SelectItem>
                    <SelectItem value="Hampshire">Hampshire</SelectItem>
                    <SelectItem value="Local Breed">Local Breed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pigs Gallery */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredPigs.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <LucideUsers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No pigs found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || genderFilter !== 'all' || breedFilter !== 'all'
                  ? "Try adjusting your search or filters"
                  : "Get started by adding your first pig to the herd"}
              </p>
              {!searchTerm && genderFilter === 'all' && breedFilter === 'all' && (
                <Link href="/pigs/add">
                  <Button className="gap-2 bg-pink-600 hover:bg-pink-700">
                    <LucidePlus className="h-4 w-4" />
                    Add Your First Pig
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            filteredPigs.map((pig) => (
              <Link href={`/pigs/${pig.id}`} key={pig.id} className="block hover-lift hover-glow-primary">
                <div className="pig-profile-card">
                  <img
                    src={getPigImage(pig.breed, pig.gender, pig.name)}
                    alt={`${pig.name} - ${pig.breed} ${pig.gender}`}
                    className="pig-image"
                    onError={(e) => {
                      e.currentTarget.src = `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(pig.name)}`
                    }}
                  />
                  <div className="pig-info">
                    <h3 className="text-lg font-bold">{pig.name}</h3>
                    <p className="text-sm opacity-90">
                      {pig.breed} • {pig.gender === 'Male' ? 'Boar' : 'Sow'} • {pig.age}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge
                        variant={
                          pig.status === "Healthy"
                            ? "outline"
                            : pig.status === "Sick"
                              ? "destructive"
                              : pig.status === "Injured"
                                ? "secondary"
                                : pig.status === "Quarantined"
                                  ? "default"
                                  : pig.status === "Deceased"
                                    ? "outline"
                                    : pig.status === "Pregnant"
                                      ? "default"
                                      : pig.status === "Lactating"
                                        ? "default"
                                        : "default"
                        }
                        className={
                          pig.status === "Healthy"
                            ? "badge-healthy"
                            : pig.status === "Sick"
                              ? "badge-sick"
                              : pig.status === "Injured"
                                ? "badge-injured"
                                : pig.status === "Quarantined"
                                  ? "badge-quarantined"
                                  : pig.status === "Deceased"
                                    ? "badge-deceased"
                                    : pig.status === "Pregnant"
                                      ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                      : pig.status === "Lactating"
                                        ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                        : ""
                        }
                      >
                        {pig.status}
                      </Badge>
                      <span className="text-xs">{pig.tag}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
