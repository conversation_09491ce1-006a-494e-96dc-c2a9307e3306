import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received feeding record data:', data);

    // Validate required fields
    if (!data.date || !data.time || data.goatGroups.length === 0 || data.feedItems.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: date, time, goatGroups, and feedItems are required' },
        { status: 400 }
      );
    }

    // Get a connection from the pool
    const connection = await pool.getConnection();

    try {
      // Start transaction
      await connection.beginTransaction();

      // Generate a unique ID for the feeding record
      const timestamp = new Date().getTime();
      const recordId = `FR-${timestamp}`;

      // Insert into feeding_records table
      await connection.execute(
        `INSERT INTO feeding_records (
          id, record_type, schedule_id, date, time,
          consumption_level, water_refilled, notes, recorded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          recordId,
          data.recordType,
          data.scheduleId || null,
          data.date,
          data.time,
          data.consumptionLevel || 'normal',
          data.waterRefilled ? 1 : 0,
          data.notes || null,
          data.recordedBy || 'System'
        ]
      );

      // Check if goat groups exist and insert them
      if (data.goatGroups && Array.isArray(data.goatGroups) && data.goatGroups.length > 0) {
        try {
          // First check if the table exists
          const [tables] = await connection.execute(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'feeding_record_goat_groups'
          `);

          // Create the table if it doesn't exist
          if (tables.length === 0) {
            await connection.execute(`
              CREATE TABLE IF NOT EXISTS feeding_record_goat_groups (
                id INT AUTO_INCREMENT PRIMARY KEY,
                feeding_record_id VARCHAR(50) NOT NULL,
                goat_group_id VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
              )
            `);
            console.log('Created feeding_record_goat_groups table');
          }

          // Insert goat groups
          for (const groupId of data.goatGroups) {
            await connection.execute(
              `INSERT INTO feeding_record_goat_groups (
                feeding_record_id, goat_group_id
              ) VALUES (?, ?)`,
              [recordId, groupId]
            );
          }
        } catch (error) {
          console.warn('Error inserting goat groups:', error);
          // Continue execution even if goat groups insertion fails
        }
      }

      // Check if feed items exist and insert them
      if (data.feedItems && Array.isArray(data.feedItems) && data.feedItems.length > 0) {
        try {
          // First check if the table exists
          const [tables] = await connection.execute(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'feeding_record_feed_items'
          `);

          // Create the table if it doesn't exist
          if (tables.length === 0) {
            await connection.execute(`
              CREATE TABLE IF NOT EXISTS feeding_record_feed_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                feeding_record_id VARCHAR(50) NOT NULL,
                feed_item_id VARCHAR(50) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                unit VARCHAR(20) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
              )
            `);
            console.log('Created feeding_record_feed_items table');
          }

          // Insert feed items
          for (const item of data.feedItems) {
            if (item.feedId && item.amount) {
              await connection.execute(
                `INSERT INTO feeding_record_feed_items (
                  feeding_record_id, feed_item_id, amount, unit
                ) VALUES (?, ?, ?, ?)`,
                [recordId, item.feedId, item.amount, item.unit || 'kg']
              );
            }
          }
        } catch (error) {
          console.warn('Error inserting feed items:', error);
          // Continue execution even if feed items insertion fails
        }
      }

      // Commit the transaction
      await connection.commit();

      return NextResponse.json({
        success: true,
        message: 'Feeding record created successfully',
        id: recordId
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      throw error;
    } finally {
      // Release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error creating feeding record:', error);
    return NextResponse.json(
      { error: `Failed to create feeding record: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Query to get all feeding records with related data
    const [rows] = await pool.execute(`
      SELECT
        fr.id, fr.record_type, fr.schedule_id, fr.date, fr.time,
        fr.consumption_level, fr.water_refilled, fr.notes, fr.recorded_by,
        fr.created_at,
        DATE_FORMAT(fr.date, '%Y-%m-%d') as formatted_date,
        DATE_FORMAT(fr.time, '%H:%i') as formatted_time
      FROM feeding_records fr
      ORDER BY fr.date DESC, fr.time DESC
      LIMIT 50
    `);

    // Get feeding statistics
    const [stats] = await pool.execute(`
      SELECT
        COUNT(*) as total_records,
        SUM(CASE WHEN consumption_level = 'low' THEN 1 ELSE 0 END) as low_consumption,
        SUM(CASE WHEN consumption_level = 'normal' THEN 1 ELSE 0 END) as normal_consumption,
        SUM(CASE WHEN consumption_level = 'high' THEN 1 ELSE 0 END) as high_consumption,
        SUM(CASE WHEN water_refilled = 1 THEN 1 ELSE 0 END) as water_refilled_count,
        COUNT(DISTINCT DATE(date)) as feeding_days
      FROM feeding_records
    `);

    // Get recent feeding records (last 7 days)
    const [recentRecords] = await pool.execute(`
      SELECT
        DATE(date) as feeding_date,
        COUNT(*) as record_count,
        SUM(CASE WHEN consumption_level = 'low' THEN 1 ELSE 0 END) as low_count,
        SUM(CASE WHEN consumption_level = 'normal' THEN 1 ELSE 0 END) as normal_count,
        SUM(CASE WHEN consumption_level = 'high' THEN 1 ELSE 0 END) as high_count
      FROM feeding_records
      WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(date)
      ORDER BY DATE(date) DESC
    `);

    // For each record, get the associated goat groups and feed items
    const records = await Promise.all(rows.map(async (record) => {
      // Get goat groups for this record
      let groupRows = [];
      try {
        [groupRows] = await pool.execute(`
          SELECT gg.id, gg.name
          FROM feeding_record_goat_groups frgg
          JOIN goat_groups gg ON frgg.goat_group_id = gg.id
          WHERE frgg.feeding_record_id = ?
        `, [record.id]);
      } catch (error) {
        console.warn(`Error fetching goat groups for record ${record.id}:`, error);
      }

      // Get feed items for this record
      let feedRows = [];
      try {
        [feedRows] = await pool.execute(`
          SELECT fi.id, fi.name, frfi.amount, frfi.unit
          FROM feeding_record_feed_items frfi
          JOIN feed_items fi ON frfi.feed_item_id = fi.id
          WHERE frfi.feeding_record_id = ?
        `, [record.id]);
      } catch (error) {
        console.warn(`Error fetching feed items for record ${record.id}:`, error);
      }

      // Get schedule name if applicable
      let scheduleName = null;
      if (record.schedule_id) {
        try {
          const [scheduleRows] = await pool.execute(`
            SELECT name FROM feeding_schedules WHERE id = ?
          `, [record.schedule_id]);

          if (scheduleRows.length > 0) {
            scheduleName = scheduleRows[0].name;
          }
        } catch (error) {
          console.warn(`Error fetching schedule for record ${record.id}:`, error);
        }
      }

      return {
        ...record,
        goatGroups: groupRows || [],
        feedItems: feedRows || [],
        scheduleName
      };
    }));

    return NextResponse.json({
      records,
      stats: stats[0],
      recentRecords
    });
  } catch (error) {
    console.error('Error fetching feeding records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feeding records: ' + error.message },
      { status: 500 }
    );
  }
}