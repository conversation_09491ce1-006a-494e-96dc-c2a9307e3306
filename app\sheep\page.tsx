"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import DashboardLayout from "@/components/dashboard-layout"
import { Badge } from "@/components/ui/badge"
import {
  LucideFilter,
  LucidePlus,
  LucideSearch,
  LucideRefreshCw,
  LucideChevronDown,
  LucideX,
  LucideLoader2,
  LucideAlertTriangle
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"
import { useSearch } from "@/hooks/use-search"

// Define the Sheep interface
interface Sheep {
  id: number;
  name: string;
  tag_number: string;
  breed: string;
  gender: string;
  age: string;
  status: string;
  [key: string]: any; // Allow additional properties
}

function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <div className="p-6 bg-red-50 rounded-lg border border-red-200">
      <h2 className="text-lg font-semibold text-red-800 mb-2">Something went wrong:</h2>
      <pre className="text-sm text-red-600 overflow-auto p-2 bg-red-100 rounded">{error.message}</pre>
      <Button variant="outline" className="mt-4" onClick={resetErrorBoundary}>
        Try again
      </Button>
    </div>
  )
}

// Skeleton loader for sheep cards
function SheepCardSkeleton() {
  return (
    <div className="goat-profile-card animate-pulse">
      <div className="h-48 w-full bg-gray-200"></div>
      <div className="goat-info">
        <div className="h-5 w-24 bg-white/30 rounded mb-2"></div>
        <div className="h-4 w-32 bg-white/30 rounded mb-4"></div>
        <div className="flex items-center justify-between">
          <div className="h-6 w-16 bg-white/30 rounded"></div>
          <div className="h-4 w-10 bg-white/30 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export default function SheepPage() {
  const [breedFilter, setBreedFilter] = useState("all")
  const [genderFilter, setGenderFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeView, setActiveView] = useState("grid")
  const [isLoading, setIsLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [sheepData, setSheepData] = useState<Sheep[]>([])
  const [stats, setStats] = useState({
    total: 0,
    healthy: 0,
    sick: 0,
    injured: 0,
    quarantined: 0,
    males: 0,
    females: 0
  })
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSheep()
  }, [genderFilter])

  const fetchSheep = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams()
      if (genderFilter !== 'all') {
        params.append('gender', genderFilter)
      }

      const response = await fetch(`/api/sheep?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch sheep')
      }

      const data = await response.json()
      setSheepData(data.sheep || [])
      setStats(data.stats || stats)
    } catch (error) {
      console.error('Error fetching sheep:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredSheep = sheepData.filter(sheep => {
    const matchesSearch = sheep.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sheep.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sheep.tag_number.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesBreed = breedFilter === "all" || sheep.breed === breedFilter

    return matchesSearch && matchesBreed
  })

  // Function to get appropriate sheep image based on breed and gender
  const getSheepImage = (breed: string, gender: string, name: string) => {
    const seed = `${name}-${breed}-${gender}`.toLowerCase().replace(/\s+/g, '-')
    
    // Sheep breeds - different colors for different breeds
    if (breed.toLowerCase().includes('dorper')) {
      // Dorper sheep - white with black head
      return `https://via.placeholder.com/300x200/F5F5DC/000000?text=${encodeURIComponent(`${breed} ${gender} Sheep`)}`
    } else if (breed.toLowerCase().includes('merino')) {
      // Merino sheep - white wool
      return `https://via.placeholder.com/300x200/FFFFFF/8B4513?text=${encodeURIComponent(`${breed} ${gender} Sheep`)}`
    } else if (breed.toLowerCase().includes('local')) {
      // Local breeds - mixed colors
      return `https://via.placeholder.com/300x200/D2B48C/654321?text=${encodeURIComponent(`${breed} ${gender} Sheep`)}`
    } else {
      // General sheep - neutral colors
      return `https://via.placeholder.com/300x200/F0F8FF/4682B4?text=${encodeURIComponent(`${breed} ${gender} Sheep`)}`
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading sheep...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="gap-2">
                <LucideArrowLeft className="h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Sheep Management</h1>
              <p className="text-muted-foreground">
                Manage your sheep flock with comprehensive tracking and care records
              </p>
            </div>
          </div>
          <Link href="/sheep/add">
            <Button className="gap-2 bg-blue-600 hover:bg-blue-700">
              <LucidePlus className="h-4 w-4" />
              Add Sheep
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sheep</CardTitle>
              <LucideUsers className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.males} males, {stats.females} females
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Healthy</CardTitle>
              <LucideHeart className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.healthy}</div>
              <p className="text-xs text-muted-foreground">
                {stats.total > 0 ? Math.round((stats.healthy / stats.total) * 100) : 0}% of flock
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Need Attention</CardTitle>
              <LucideActivity className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.sick + stats.injured + stats.quarantined}
              </div>
              <p className="text-xs text-muted-foreground">
                Sick, injured, or quarantined
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Breeding</CardTitle>
              <LucideHeart className="h-4 w-4 text-pink-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-pink-600">
                {stats.pregnant + stats.lactating}
              </div>
              <p className="text-xs text-muted-foreground">
                Pregnant or lactating
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LucideFilter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <LucideSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search sheep by name, breed, or tag..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={genderFilter} onValueChange={setGenderFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    <SelectItem value="Male">Male</SelectItem>
                    <SelectItem value="Female">Female</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={breedFilter} onValueChange={setBreedFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Breed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Breeds</SelectItem>
                    <SelectItem value="Dorper">Dorper</SelectItem>
                    <SelectItem value="Merino">Merino</SelectItem>
                    <SelectItem value="Local Breed">Local Breed</SelectItem>
                    <SelectItem value="Romney">Romney</SelectItem>
                    <SelectItem value="Suffolk">Suffolk</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sheep Gallery */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredSheep.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <LucideUsers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No sheep found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || genderFilter !== 'all' || breedFilter !== 'all'
                  ? "Try adjusting your search or filters"
                  : "Get started by adding your first sheep to the flock"}
              </p>
              {!searchTerm && genderFilter === 'all' && breedFilter === 'all' && (
                <Link href="/sheep/add">
                  <Button className="gap-2 bg-blue-600 hover:bg-blue-700">
                    <LucidePlus className="h-4 w-4" />
                    Add Your First Sheep
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            filteredSheep.map((sheep) => (
              <Link href={`/sheep/${sheep.id}`} key={sheep.id} className="block hover-lift hover-glow-primary">
                <div className="sheep-profile-card">
                  <img
                    src={getSheepImage(sheep.breed, sheep.gender, sheep.name)}
                    alt={`${sheep.name} - ${sheep.breed} ${sheep.gender}`}
                    className="sheep-image"
                    onError={(e) => {
                      e.currentTarget.src = `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(sheep.name)}`
                    }}
                  />
                  <div className="sheep-info">
                    <h3 className="text-lg font-bold">{sheep.name}</h3>
                    <p className="text-sm opacity-90">
                      {sheep.breed} • {sheep.gender} • {sheep.age}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge
                        variant={
                          sheep.status === "Healthy"
                            ? "outline"
                            : sheep.status === "Sick"
                              ? "destructive"
                              : sheep.status === "Injured"
                                ? "secondary"
                                : sheep.status === "Quarantined"
                                  ? "default"
                                  : sheep.status === "Deceased"
                                    ? "outline"
                                    : sheep.status === "Pregnant"
                                      ? "default"
                                      : sheep.status === "Lactating"
                                        ? "default"
                                        : "default"
                        }
                        className={
                          sheep.status === "Healthy"
                            ? "badge-healthy"
                            : sheep.status === "Sick"
                              ? "badge-sick"
                              : sheep.status === "Injured"
                                ? "badge-injured"
                                : sheep.status === "Quarantined"
                                  ? "badge-quarantined"
                                  : sheep.status === "Deceased"
                                    ? "badge-deceased"
                                    : sheep.status === "Pregnant"
                                      ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                      : sheep.status === "Lactating"
                                        ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                        : ""
                        }
                      >
                        {sheep.status}
                      </Badge>
                      <span className="text-xs">{sheep.tag}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
