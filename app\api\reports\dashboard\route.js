import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
});

export async function GET(request) {
  let connection;

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30'; // Default to 30 days
    const reportType = searchParams.get('reportType') || 'all'; // Default to all reports

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(dateRange));

    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    // Get a connection from the pool
    connection = await pool.getConnection();

    // Initialize response object
    const response = {
      summary: {},
      financial: {},
      health: {},
      breeding: {},
      inventory: {},
      goats: {}
    };

    try {
      // Get financial summary
      const [financialSummary] = await connection.execute(`
        SELECT
          SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as total_revenue,
          SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expenses,
          SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END) as net_profit
        FROM
          financial_transactions
        WHERE
          transaction_date BETWEEN ? AND ?
      `, [formattedStartDate, formattedEndDate]);

      // Calculate profit margin
      const totalRevenue = financialSummary[0]?.total_revenue || 0;
      const totalExpenses = financialSummary[0]?.total_expenses || 0;
      const netProfit = financialSummary[0]?.net_profit || 0;
      const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

      response.summary.totalRevenue = totalRevenue;
      response.summary.totalExpenses = totalExpenses;
      response.summary.netProfit = netProfit;
      response.summary.profitMargin = parseFloat(profitMargin.toFixed(1));

      // Get health issues count - total number of health records
      const [healthIssues] = await connection.execute(`
        SELECT
          COUNT(*) as count
        FROM
          health_records
        WHERE
          record_date BETWEEN ? AND ?
      `, [formattedStartDate, formattedEndDate]);

      response.summary.healthIssues = healthIssues[0]?.count || 0;

      // Get birth rate
      const [birthingRecords] = await connection.execute(`
        SELECT
          COUNT(*) as total_births,
          SUM(CASE WHEN status = 'Kidded' THEN 1 ELSE 0 END) as successful_births
        FROM
          breeding_records
        WHERE
          actual_kidding_date BETWEEN ? AND ?
          OR expected_kidding_date BETWEEN ? AND ?
      `, [formattedStartDate, formattedEndDate, formattedStartDate, formattedEndDate]);

      const totalBirths = birthingRecords[0]?.total_births || 0;
      const successfulBirths = birthingRecords[0]?.successful_births || 0;
      const birthRate = totalBirths > 0 ? (successfulBirths / totalBirths) * 100 : 0;

      response.summary.birthRate = parseFloat(birthRate.toFixed(1));

      // Get inventory value
      const [inventoryValue] = await connection.execute(`
        SELECT
          SUM(quantity * price_per_unit) as total_value
        FROM
          inventory_items
        WHERE
          price_per_unit IS NOT NULL
      `);

      response.summary.inventoryValue = inventoryValue[0]?.total_value || 0;

      // Get feed costs
      const [feedCosts] = await connection.execute(`
        SELECT
          SUM(amount) as total_feed_costs
        FROM
          financial_transactions
        WHERE
          transaction_date BETWEEN ? AND ?
          AND transaction_type = 'expense'
          AND category = 'Feed'
      `, [formattedStartDate, formattedEndDate]);

      response.summary.feedCosts = feedCosts[0]?.total_feed_costs || 0;

      // Get monthly financial data for charts
      const [monthlyFinancial] = await connection.execute(`
        SELECT
          DATE_FORMAT(transaction_date, '%b') as month,
          SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as revenue,
          SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as expenses,
          SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END) as profit
        FROM
          financial_transactions
        WHERE
          transaction_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY
          DATE_FORMAT(transaction_date, '%Y-%m')
        ORDER BY
          transaction_date
      `);

      response.financial.monthlyData = monthlyFinancial;

      // Get expense breakdown
      const [expenseBreakdown] = await connection.execute(`
        SELECT
          category,
          SUM(amount) as total
        FROM
          financial_transactions
        WHERE
          transaction_date BETWEEN ? AND ?
          AND transaction_type = 'expense'
        GROUP BY
          category
        ORDER BY
          total DESC
      `, [formattedStartDate, formattedEndDate]);

      response.financial.expenseBreakdown = expenseBreakdown;

      // Get revenue sources
      const [revenueSources] = await connection.execute(`
        SELECT
          category,
          SUM(amount) as total
        FROM
          financial_transactions
        WHERE
          transaction_date BETWEEN ? AND ?
          AND transaction_type = 'income'
        GROUP BY
          category
        ORDER BY
          total DESC
      `, [formattedStartDate, formattedEndDate]);

      response.financial.revenueSources = revenueSources;

      // Get health data
      const [healthData] = await connection.execute(`
        SELECT
          DATE_FORMAT(record_date, '%b') as month,
          COUNT(CASE WHEN record_type = 'Illness' THEN 1 END) as illnesses,
          COUNT(CASE WHEN record_type = 'Vaccination' THEN 1 END) as vaccinations,
          COUNT(CASE WHEN record_type = 'Medication' THEN 1 END) as treatments
        FROM
          health_records
        WHERE
          record_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY
          DATE_FORMAT(record_date, '%Y-%m')
        ORDER BY
          record_date
      `);

      response.health.monthlyData = healthData;

      // Get common health issues
      const [healthIssuesBreakdown] = await connection.execute(`
        SELECT
          record_type,
          COUNT(*) as count
        FROM
          health_records
        WHERE
          record_date BETWEEN ? AND ?
        GROUP BY
          record_type
        ORDER BY
          count DESC
      `, [formattedStartDate, formattedEndDate]);

      response.health.issuesBreakdown = healthIssuesBreakdown;

      // Get breeding data
      const [breedingData] = await connection.execute(`
        SELECT
          DATE_FORMAT(breeding_date, '%b') as month,
          COUNT(*) as breeding_attempts,
          SUM(CASE WHEN status IN ('Confirmed', 'Kidded') THEN 1 ELSE 0 END) as successful_pregnancies,
          SUM(number_of_kids) as kids_born
        FROM
          breeding_records
        WHERE
          breeding_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY
          DATE_FORMAT(breeding_date, '%Y-%m')
        ORDER BY
          breeding_date
      `);

      response.breeding.monthlyData = breedingData;

      // Get breeding success by breed
      const [breedingByBreed] = await connection.execute(`
        SELECT
          g.breed,
          COUNT(br.id) as total_attempts,
          SUM(CASE WHEN br.status IN ('Confirmed', 'Kidded') THEN 1 ELSE 0 END) as successful
        FROM
          breeding_records br
        JOIN
          goats g ON br.doe_id = g.id
        WHERE
          br.breeding_date BETWEEN ? AND ?
        GROUP BY
          g.breed
        ORDER BY
          successful DESC
      `, [formattedStartDate, formattedEndDate]);

      // Calculate success rates
      response.breeding.breedingByBreed = breedingByBreed.map(item => ({
        ...item,
        success_rate: item.total_attempts > 0
          ? parseFloat(((item.successful / item.total_attempts) * 100).toFixed(1))
          : 0
      }));

      // Get kids per birth by breed
      const [kidsPerBirth] = await connection.execute(`
        SELECT
          g.breed,
          AVG(br.number_of_kids) as avg_kids
        FROM
          breeding_records br
        JOIN
          goats g ON br.doe_id = g.id
        WHERE
          br.status = 'Kidded'
          AND br.actual_kidding_date BETWEEN ? AND ?
        GROUP BY
          g.breed
        ORDER BY
          avg_kids DESC
      `, [formattedStartDate, formattedEndDate]);

      response.breeding.kidsPerBirth = kidsPerBirth.map(item => ({
        ...item,
        avg_kids: parseFloat(item.avg_kids.toFixed(1))
      }));

      // Get goat population data
      const [goatsByBreed] = await connection.execute(`
        SELECT
          breed,
          COUNT(*) as count
        FROM
          goats
        WHERE
          status = 'Active'
        GROUP BY
          breed
        ORDER BY
          count DESC
      `);

      response.goats.breedDistribution = goatsByBreed;

      // Get goats by gender
      const [goatsByGender] = await connection.execute(`
        SELECT
          gender,
          COUNT(*) as count
        FROM
          goats
        WHERE
          status = 'Active'
        GROUP BY
          gender
      `);

      response.goats.genderDistribution = goatsByGender;

      return NextResponse.json(response);

    } catch (queryError) {
      console.error('Error executing database query:', queryError);
      return NextResponse.json(
        { error: 'Database query error: ' + queryError.message },
        { status: 500 }
      );
    } finally {
      // Release the connection
      if (connection) connection.release();
    }
  } catch (error) {
    console.error('Error in reports dashboard API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports data: ' + error.message },
      { status: 500 }
    );
  }
}
