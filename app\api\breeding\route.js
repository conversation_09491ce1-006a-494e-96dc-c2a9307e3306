import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received breeding record data:', data);
    
    // Validate required fields
    if (!data.doe_id || !data.breeding_date) {
      console.log('Missing required fields:', { doe_id: data.doe_id, breeding_date: data.breeding_date });
      return NextResponse.json(
        { error: 'Missing required fields: doe_id and breeding_date are required' },
        { status: 400 }
      );
    }
    
    // Get a connection from the pool
    const connection = await pool.getConnection();
    
    try {
      // Start transaction
      await connection.beginTransaction();
      
      // Insert data into the breeding_records table
      const [result] = await connection.execute(
        `INSERT INTO breeding_records (
          doe_id,
          buck_id,
          breeding_method,
          breeding_date,
          expected_kidding_date,
          actual_kidding_date,
          number_of_kids,
          notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          data.doe_id,
          data.buck_id || null,
          data.breeding_method || 'natural',
          data.breeding_date,
          data.expected_kidding_date || null,
          data.actual_kidding_date || null,
          data.number_of_kids || null,
          data.notes || null
        ]
      );
      
      // Commit transaction
      await connection.commit();
      
      // Get the inserted ID
      const breedingRecordId = result.insertId;
      console.log('Breeding record inserted with ID:', breedingRecordId);
      
      return NextResponse.json({ 
        success: true, 
        message: 'Breeding record created successfully',
        id: breedingRecordId
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      throw error;
    } finally {
      // Release connection back to the pool
      connection.release();
    }
  } catch (error) {
    console.error('Error creating breeding record:', error);
    return NextResponse.json(
      { error: `Failed to create breeding record: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const [rows] = await pool.execute(`
      SELECT br.*, 
             d.name as doe_name, 
             b.name as buck_name 
      FROM breeding_records br
      LEFT JOIN goats d ON br.doe_id = d.id
      LEFT JOIN goats b ON br.buck_id = b.id
      ORDER BY br.breeding_date DESC
    `);
    
    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching breeding records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch breeding records' },
      { status: 500 }
    );
  }
}
