'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LucideSearch, LucidePlus, LucideFilter, LucideUsers, LucideHeart, LucideActivity, LucideArrowLeft } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

interface Cattle {
  id: number
  name: string
  breed: string
  gender: string
  status: string
  age: string
  tag: string
}

interface CattleStats {
  total: number
  healthy: number
  sick: number
  injured: number
  quarantined: number
  pregnant: number
  lactating: number
  males: number
  females: number
}

export default function CattlePage() {
  const [cattle, setCattle] = useState<Cattle[]>([])
  const [stats, setStats] = useState<CattleStats>({
    total: 0,
    healthy: 0,
    sick: 0,
    injured: 0,
    quarantined: 0,
    pregnant: 0,
    lactating: 0,
    males: 0,
    females: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [genderFilter, setGenderFilter] = useState("all")
  const [breedFilter, setBreedFilter] = useState("all")

  useEffect(() => {
    fetchCattle()
  }, [genderFilter])

  const fetchCattle = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (genderFilter !== 'all') {
        params.append('gender', genderFilter)
      }

      const response = await fetch(`/api/cattle?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch cattle')
      }

      const data = await response.json()
      setCattle(data.cattle || [])
      setStats(data.stats || stats)
    } catch (error) {
      console.error('Error fetching cattle:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredCattle = cattle.filter(cattle => {
    const matchesSearch = cattle.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cattle.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cattle.tag.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesBreed = breedFilter === "all" || cattle.breed === breedFilter

    return matchesSearch && matchesBreed
  })

  // Function to get appropriate cattle image based on breed and gender
  const getCattleImage = (breed: string, gender: string, name: string) => {
    const seed = `${name}-${breed}-${gender}`.toLowerCase().replace(/\s+/g, '-')
    
    // Cattle breeds - different colors for different breeds
    if (breed.toLowerCase().includes('holstein')) {
      // Holstein cattle - black and white
      return `https://via.placeholder.com/300x200/000000/FFFFFF?text=${encodeURIComponent(`${breed} ${gender} Cattle`)}`
    } else if (breed.toLowerCase().includes('angus')) {
      // Angus cattle - black
      return `https://via.placeholder.com/300x200/2F2F2F/FFFFFF?text=${encodeURIComponent(`${breed} ${gender} Cattle`)}`
    } else if (breed.toLowerCase().includes('zebu') || breed.toLowerCase().includes('local')) {
      // Zebu/Local breeds - brown/tan
      return `https://via.placeholder.com/300x200/8B4513/FFFFFF?text=${encodeURIComponent(`${breed} ${gender} Cattle`)}`
    } else if (breed.toLowerCase().includes('jersey')) {
      // Jersey cattle - light brown
      return `https://via.placeholder.com/300x200/D2B48C/654321?text=${encodeURIComponent(`${breed} ${gender} Cattle`)}`
    } else {
      // General cattle - neutral colors
      return `https://via.placeholder.com/300x200/8B4513/F5DEB3?text=${encodeURIComponent(`${breed} ${gender} Cattle`)}`
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading cattle...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="gap-2">
                <LucideArrowLeft className="h-4 w-4" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Cattle Management</h1>
              <p className="text-muted-foreground">
                Manage your cattle herd with comprehensive tracking and care records
              </p>
            </div>
          </div>
          <Link href="/cattle/add">
            <Button className="gap-2 bg-amber-600 hover:bg-amber-700">
              <LucidePlus className="h-4 w-4" />
              Add Cattle
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Cattle</CardTitle>
              <LucideUsers className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.males} bulls, {stats.females} cows
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Healthy</CardTitle>
              <LucideHeart className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.healthy}</div>
              <p className="text-xs text-muted-foreground">
                {stats.total > 0 ? Math.round((stats.healthy / stats.total) * 100) : 0}% of herd
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Need Attention</CardTitle>
              <LucideActivity className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {stats.sick + stats.injured + stats.quarantined}
              </div>
              <p className="text-xs text-muted-foreground">
                Sick, injured, or quarantined
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Breeding</CardTitle>
              <LucideHeart className="h-4 w-4 text-pink-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-pink-600">
                {stats.pregnant + stats.lactating}
              </div>
              <p className="text-xs text-muted-foreground">
                Pregnant or lactating
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LucideFilter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <LucideSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search cattle by name, breed, or tag..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={genderFilter} onValueChange={setGenderFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    <SelectItem value="Male">Bulls</SelectItem>
                    <SelectItem value="Female">Cows</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={breedFilter} onValueChange={setBreedFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Breed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Breeds</SelectItem>
                    <SelectItem value="Holstein">Holstein</SelectItem>
                    <SelectItem value="Angus">Angus</SelectItem>
                    <SelectItem value="Zebu Cross">Zebu Cross</SelectItem>
                    <SelectItem value="Jersey">Jersey</SelectItem>
                    <SelectItem value="Brahman">Brahman</SelectItem>
                    <SelectItem value="Local Breed">Local Breed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cattle Gallery */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredCattle.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <LucideUsers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No cattle found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || genderFilter !== 'all' || breedFilter !== 'all'
                  ? "Try adjusting your search or filters"
                  : "Get started by adding your first cattle to the herd"}
              </p>
              {!searchTerm && genderFilter === 'all' && breedFilter === 'all' && (
                <Link href="/cattle/add">
                  <Button className="gap-2 bg-amber-600 hover:bg-amber-700">
                    <LucidePlus className="h-4 w-4" />
                    Add Your First Cattle
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            filteredCattle.map((cattle) => (
              <Link href={`/cattle/${cattle.id}`} key={cattle.id} className="block hover-lift hover-glow-primary">
                <div className="cattle-profile-card">
                  <img
                    src={getCattleImage(cattle.breed, cattle.gender, cattle.name)}
                    alt={`${cattle.name} - ${cattle.breed} ${cattle.gender}`}
                    className="cattle-image"
                    onError={(e) => {
                      e.currentTarget.src = `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(cattle.name)}`
                    }}
                  />
                  <div className="cattle-info">
                    <h3 className="text-lg font-bold">{cattle.name}</h3>
                    <p className="text-sm opacity-90">
                      {cattle.breed} • {cattle.gender === 'Male' ? 'Bull' : 'Cow'} • {cattle.age}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge
                        variant={
                          cattle.status === "Healthy"
                            ? "outline"
                            : cattle.status === "Sick"
                              ? "destructive"
                              : cattle.status === "Injured"
                                ? "secondary"
                                : cattle.status === "Quarantined"
                                  ? "default"
                                  : cattle.status === "Deceased"
                                    ? "outline"
                                    : cattle.status === "Pregnant"
                                      ? "default"
                                      : cattle.status === "Lactating"
                                        ? "default"
                                        : "default"
                        }
                        className={
                          cattle.status === "Healthy"
                            ? "badge-healthy"
                            : cattle.status === "Sick"
                              ? "badge-sick"
                              : cattle.status === "Injured"
                                ? "badge-injured"
                                : cattle.status === "Quarantined"
                                  ? "badge-quarantined"
                                  : cattle.status === "Deceased"
                                    ? "badge-deceased"
                                    : cattle.status === "Pregnant"
                                      ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                      : cattle.status === "Lactating"
                                        ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                        : ""
                        }
                      >
                        {cattle.status}
                      </Badge>
                      <span className="text-xs">{cattle.tag}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
