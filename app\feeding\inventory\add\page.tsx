"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { DatePicker } from "@/components/ui/date-picker"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucideWheat, LucideSave, LucideInfo } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

export default function AddFeedInventoryPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    description: "", // Add description field
    quantity: "",
    unit: "",
    minLevel: "",
    maxLevel: "",
    location: "",
    expiryDate: null,
    purchaseDate: null,
    price: "",
    supplier: "",
    protein: "",
    fiber: "",
    fat: "",
    moisture: "",
    notes: "", // Ensure notes field is initialized
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleDateChange = (field, date) => {
    // Format the date as YYYY-MM-DD if a date is selected
    if (date) {
      const formattedDate = date.toISOString().split('T')[0]
      console.log(`Date selected for ${field}:`, formattedDate)
      setFormData((prev) => ({ ...prev, [field]: formattedDate }))
    } else {
      setFormData((prev) => ({ ...prev, [field]: null }))
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log("Form submission started");

      // Convert numeric string values to actual numbers
      const processedData = {
        ...formData,
        quantity: formData.quantity ? parseFloat(formData.quantity) : 0,
        minLevel: formData.minLevel ? parseFloat(formData.minLevel) : null,
        maxLevel: formData.maxLevel ? parseFloat(formData.maxLevel) : null,
        protein: formData.protein ? parseFloat(formData.protein) : null,
        fiber: formData.fiber ? parseFloat(formData.fiber) : null,
        fat: formData.fat ? parseFloat(formData.fat) : null,
        moisture: formData.moisture ? parseFloat(formData.moisture) : null,
        price: formData.price ? parseFloat(formData.price) : null
      };

      console.log("Processed data being sent to API:", JSON.stringify(processedData, null, 2));

      // Send the data to our new API endpoint
      const response = await fetch('/api/feeding/feed-items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processedData),
        cache: 'no-store',
      });

      console.log("API response status:", response.status);

      const responseData = await response.json();
      console.log("API response data:", responseData);

      if (!response.ok) {
        throw new Error(responseData.error || `Failed with status ${response.status}`);
      }

      // Success handling
      toast({
        title: "Success",
        description: "Feed item added successfully!",
      });

      // Add a small delay before redirecting to ensure the toast is seen
      setTimeout(() => {
        router.push('/feeding/inventory');
      }, 1000);

    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: "Error",
        description: `Failed to add feed item: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Log initial form data for debugging
  useEffect(() => {
    console.log("Initial form data:", formData);
  }, []);

  return (
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Link href="/feeding">
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <path d="m12 19-7-7 7-7" />
                  <path d="M19 12H5" />
                </svg>
                <span className="sr-only">Back</span>
              </Button>
            </Link>
            <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Add Feed Inventory</h1>
          </div>
        </div>



        {/* Form */}
        <Card className="border-t-4 border-t-amber-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <LucideWheat className="h-6 w-6 text-amber-500" />
              <CardTitle>New Feed Inventory</CardTitle>
            </div>
            <CardDescription>Add a new feed item to your inventory</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      Feed Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter feed name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">
                      Category <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleSelectChange("category", value)}
                      required
                    >
                      <SelectTrigger id="category" className="input-primary">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Hay">Hay</SelectItem>
                        <SelectItem value="Grain">Grain</SelectItem>
                        <SelectItem value="Pellets">Pellets</SelectItem>
                        <SelectItem value="Supplements">Supplements</SelectItem>
                        <SelectItem value="Silage">Silage</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      placeholder="Enter feed description"
                      value={formData.description}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Quantity and Stock Levels */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Quantity and Stock Levels</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">
                      Quantity <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter quantity"
                      value={formData.quantity}
                      onChange={handleChange}
                      required
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">
                      Unit <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.unit} onValueChange={(value) => handleSelectChange("unit", value)} required>
                      <SelectTrigger id="unit" className="input-primary">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kg">Kilograms (kg)</SelectItem>
                        <SelectItem value="g">Grams (g)</SelectItem>
                        <SelectItem value="lb">Pounds (lb)</SelectItem>
                        <SelectItem value="bales">Bales</SelectItem>
                        <SelectItem value="bags">Bags</SelectItem>
                        <SelectItem value="blocks">Blocks</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Storage Location</Label>
                    <Input
                      id="location"
                      name="location"
                      placeholder="Enter storage location"
                      value={formData.location}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minLevel">Minimum Stock Level</Label>
                    <Input
                      id="minLevel"
                      name="minLevel"
                      type="number"
                      min="0"
                      placeholder="Enter minimum level"
                      value={formData.minLevel}
                      onChange={handleChange}
                      className="input-primary"
                    />
                    <p className="text-xs text-muted-foreground">
                      You'll receive alerts when stock falls below this level
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxLevel">Maximum Stock Level</Label>
                    <Input
                      id="maxLevel"
                      name="maxLevel"
                      type="number"
                      min="0"
                      placeholder="Enter maximum level"
                      value={formData.maxLevel}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Nutritional Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-medium text-amber-700">Nutritional Information</h3>
                  <div className="text-xs text-muted-foreground italic">(Optional)</div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="protein">Protein (%)</Label>
                    <Input
                      id="protein"
                      name="protein"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="Protein %"
                      value={formData.protein}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fiber">Fiber (%)</Label>
                    <Input
                      id="fiber"
                      name="fiber"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="Fiber %"
                      value={formData.fiber}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fat">Fat (%)</Label>
                    <Input
                      id="fat"
                      name="fat"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="Fat %"
                      value={formData.fat}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="moisture">Moisture (%)</Label>
                    <Input
                      id="moisture"
                      name="moisture"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="Moisture %"
                      value={formData.moisture}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                </div>
                <div className="bg-amber-50 p-4 rounded-md border border-amber-200 flex gap-3">
                  <LucideInfo className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-amber-800">
                    Tracking nutritional information helps ensure your goats receive a balanced diet. This information
                    is often available on feed packaging or from your supplier.
                  </div>
                </div>
              </div>

              {/* Purchase Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Purchase Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price per Unit</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter price"
                      value={formData.price}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="supplier">Supplier</Label>
                    <Input
                      id="supplier"
                      name="supplier"
                      placeholder="Enter supplier name"
                      value={formData.supplier}
                      onChange={handleChange}
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="purchaseDate">Purchase Date</Label>
                    <DatePicker
                      id="purchaseDate"
                      selected={formData.purchaseDate}
                      onSelect={(date) => handleDateChange("purchaseDate", date)}
                      className="input-primary"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiryDate">Expiry Date</Label>
                    <DatePicker
                      id="expiryDate"
                      selected={formData.expiryDate}
                      onSelect={(date) => handleDateChange("expiryDate", date)}
                      className="input-primary"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional notes about this feed"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-primary min-h-[100px]"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/feeding")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Save Feed Item
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </div>
  )
}





























