"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  LucideWheat,
  LucidePlus,
  LucideSearch,
  LucideFilter,
  LucideRefreshCw,
  LucideX,
  LucideAlertTriangle,
  LucideArrowUpRight,
  LucidePackage,
  LucideCalendarClock,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

// Sample feed inventory data
const feedItems = [
  {
    id: "FEED-001",
    name: "Alfalf<PERSON> Hay",
    category: "Hay",
    quantity: 750,
    unit: "kg",
    minLevel: 200,
    maxLevel: 1000,
    location: "Main Barn",
    expiryDate: "2024-09-15",
    lastRestocked: "2024-05-10",
    price: 0.35,
    supplier: "Green Valley Farms",
    nutritionalInfo: {
      protein: "16-20%",
      fiber: "25-30%",
      calcium: "1.2-1.5%",
    },
  },
  {
    id: "FEED-002",
    name: "Grain Mix",
    category: "Grain",
    quantity: 150,
    unit: "kg",
    minLevel: 50,
    maxLevel: 300,
    location: "Feed Storage",
    expiryDate: "2024-08-20",
    lastRestocked: "2024-05-05",
    price: 0.75,
    supplier: "Nutrition Plus",
    nutritionalInfo: {
      protein: "14-16%",
      fiber: "8-10%",
      fat: "3-5%",
    },
  },
  {
    id: "FEED-003",
    name: "Mineral Blocks",
    category: "Supplements",
    quantity: 15,
    unit: "blocks",
    minLevel: 5,
    maxLevel: 30,
    location: "Feed Storage",
    expiryDate: "2025-01-10",
    lastRestocked: "2024-04-15",
    price: 8.5,
    supplier: "Goat Nutrition Co.",
    nutritionalInfo: {
      calcium: "16%",
      phosphorus: "8%",
      salt: "35-40%",
    },
  },
  {
    id: "FEED-004",
    name: "Alfalfa Pellets",
    category: "Pellets",
    quantity: 200,
    unit: "kg",
    minLevel: 75,
    maxLevel: 400,
    location: "Feed Storage",
    expiryDate: "2024-10-25",
    lastRestocked: "2024-05-20",
    price: 0.65,
    supplier: "Farm Supply Co.",
    nutritionalInfo: {
      protein: "15-17%",
      fiber: "24-28%",
      calcium: "1.0-1.3%",
    },
  },
  {
    id: "FEED-005",
    name: "Grass Hay",
    category: "Hay",
    quantity: 500,
    unit: "kg",
    minLevel: 150,
    maxLevel: 800,
    location: "Main Barn",
    expiryDate: "2024-11-30",
    lastRestocked: "2024-05-15",
    price: 0.28,
    supplier: "Green Valley Farms",
    nutritionalInfo: {
      protein: "8-10%",
      fiber: "30-35%",
      calcium: "0.4-0.6%",
    },
  },
  {
    id: "FEED-006",
    name: "Protein Supplement",
    category: "Supplements",
    quantity: 45,
    unit: "kg",
    minLevel: 20,
    maxLevel: 100,
    location: "Feed Storage",
    expiryDate: "2024-07-10",
    lastRestocked: "2024-03-25",
    price: 1.25,
    supplier: "Nutrition Plus",
    nutritionalInfo: {
      protein: "30-35%",
      fat: "5-7%",
      fiber: "5-8%",
    },
  },
]

export default function FeedInventoryPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockLevelFilter, setStockLevelFilter] = useState("all")

  // Calculate low stock items
  const lowStockItems = feedItems.filter((item) => item.quantity <= item.minLevel)

  // Calculate expiring soon items (within 30 days)
  const today = new Date()
  const thirtyDaysFromNow = new Date(today)
  thirtyDaysFromNow.setDate(today.getDate() + 30)

  const expiringSoonItems = feedItems.filter((item) => {
    if (!item.expiryDate) return false
    const expiryDate = new Date(item.expiryDate)
    return expiryDate <= thirtyDaysFromNow && expiryDate >= today
  })

  // Calculate total inventory value
  const totalInventoryValue = feedItems.reduce((total, item) => total + item.quantity * item.price, 0)

  // Filter feed items based on search and filters
  const filteredItems = feedItems.filter((item) => {
    const matchesSearch =
      searchTerm === "" ||
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter

    const stockLevel = item.quantity <= item.minLevel ? "low" : item.quantity >= item.maxLevel * 0.9 ? "high" : "normal"
    const matchesStockLevel = stockLevelFilter === "all" || stockLevel === stockLevelFilter

    return matchesSearch && matchesCategory && matchesStockLevel
  })

  // Get category counts
  const categoryCounts = {
    Hay: feedItems.filter((item) => item.category === "Hay").length,
    Grain: feedItems.filter((item) => item.category === "Grain").length,
    Pellets: feedItems.filter((item) => item.category === "Pellets").length,
    Supplements: feedItems.filter((item) => item.category === "Supplements").length,
  }

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("")
    setCategoryFilter("all")
    setStockLevelFilter("all")
  }

  // Get stock level badge
  const getStockLevelBadge = (item) => {
    const stockPercentage = (item.quantity / item.maxLevel) * 100

    if (item.quantity <= item.minLevel) {
      return (
        <Badge variant="outline" className="badge-sick">
          Low Stock
        </Badge>
      )
    } else if (stockPercentage >= 90) {
      return (
        <Badge variant="outline" className="badge-healthy">
          Well Stocked
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="badge-secondary">
          Adequate
        </Badge>
      )
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A"
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Feed Inventory</h1>
          <div className="flex gap-2">
            <Link href="/feeding/inventory/transaction">
              <Button
                variant="outline"
                className="border-2 border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700 transition-all duration-300"
              >
                <LucidePackage className="mr-2 h-4 w-4" />
                Record Transaction
              </Button>
            </Link>
            <Link href="/feeding/inventory/add">
              <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add Feed Item
              </Button>
            </Link>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Total Items</div>
                  <div className="text-2xl font-bold text-amber-600">{feedItems.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideWheat className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Inventory Value</div>
                  <div className="text-2xl font-bold text-amber-600">${totalInventoryValue.toFixed(2)}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucidePackage className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Low Stock Items</div>
                  <div className="text-2xl font-bold text-red-600">{lowStockItems.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                  <LucideAlertTriangle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Expiring Soon</div>
                  <div className="text-2xl font-bold text-orange-600">{expiringSoonItems.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                  <LucideCalendarClock className="h-5 w-5 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="overflow-hidden">
          <CardHeader className="p-4 pb-0">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle>Feed Inventory</CardTitle>
              <div className="flex items-center gap-2">
                <LucideFilter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Filter items</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4">
              <div className="relative">
                <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, ID or category..."
                  className="pl-10 pr-10 input-primary"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-amber-50 hover:text-amber-700"
                    onClick={() => setSearchTerm("")}
                  >
                    <LucideX className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="Hay">Hay</SelectItem>
                      <SelectItem value="Grain">Grain</SelectItem>
                      <SelectItem value="Pellets">Pellets</SelectItem>
                      <SelectItem value="Supplements">Supplements</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={stockLevelFilter} onValueChange={setStockLevelFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Stock Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stock Levels</SelectItem>
                      <SelectItem value="low">Low Stock</SelectItem>
                      <SelectItem value="normal">Normal Stock</SelectItem>
                      <SelectItem value="high">High Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700"
                  onClick={resetFilters}
                  disabled={categoryFilter === "all" && stockLevelFilter === "all" && !searchTerm}
                >
                  <LucideRefreshCw className="h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Feed Inventory Table */}
        <Card>
          <CardContent className="p-0 overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Stock Level</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Expiry Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.length > 0 ? (
                  filteredItems.map((item) => (
                    <TableRow key={item.id} className="hover-lift">
                      <TableCell className="font-medium">{item.id}</TableCell>
                      <TableCell>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {item.nutritionalInfo.protein && `Protein: ${item.nutritionalInfo.protein}`}
                        </div>
                      </TableCell>
                      <TableCell>{item.category}</TableCell>
                      <TableCell>
                        {item.quantity} {item.unit}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {getStockLevelBadge(item)}
                          <Progress
                            value={(item.quantity / item.maxLevel) * 100}
                            className={`h-2 ${
                              item.quantity <= item.minLevel
                                ? "bg-red-100"
                                : item.quantity >= item.maxLevel * 0.9
                                  ? "bg-green-100"
                                  : "bg-amber-100"
                            }`}
                          />
                        </div>
                      </TableCell>
                      <TableCell>{item.location}</TableCell>
                      <TableCell>{formatDate(item.expiryDate)}</TableCell>
                      <TableCell className="text-right">
                        <Link href={`/feeding/inventory/${item.id}`}>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="bg-gradient-to-r from-amber-50 to-yellow-50 text-amber-600 hover:from-amber-100 hover:to-yellow-100 hover:text-amber-700"
                          >
                            View
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-6">
                      <div className="flex flex-col items-center justify-center p-8">
                        <div className="rounded-full bg-muted p-3 mb-4">
                          <LucideSearch className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-1">No feed items found</h3>
                        <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                        <Button variant="outline" onClick={resetFilters}>
                          Reset Filters
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Low Stock Alerts */}
        {lowStockItems.length > 0 && (
          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle>Low Stock Alerts</CardTitle>
                <LucideAlertTriangle className="h-5 w-5 text-red-500" />
              </div>
              <CardDescription>Items that need to be restocked soon</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {lowStockItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-2 bg-red-50 rounded-md">
                    <div className="flex items-center gap-2">
                      <LucideWheat className="h-4 w-4 text-red-500" />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-red-600 font-medium">
                        {item.quantity} / {item.minLevel} {item.unit}
                      </span>
                      <Link href={`/feeding/inventory/${item.id}`}>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-600">
                          <LucideArrowUpRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Link href="/feeding/inventory/transaction" className="w-full">
                <Button className="w-full bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white">
                  Restock Inventory
                </Button>
              </Link>
            </CardFooter>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}

