import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');

    console.log('GET /api/sheep - Gender filter:', gender);

    // Query to get all sheep with calculated age
    let query = `
      SELECT
        id,
        tag_number,
        name,
        breed,
        gender,
        status,
        birth_date,
        weight,
        color,
        markings,
        notes,
        CASE
          WHEN birth_date IS NOT NULL THEN
            CONCAT(
              FLOOR(DATEDIFF(CURDATE(), birth_date) / 365),
              ' years, ',
              FLOOR((DATEDIFF(CURDATE(), birth_date) % 365) / 30),
              ' months'
            )
          ELSE 'Unknown'
        END as age
      FROM animals
      WHERE animal_type = 'Sheep'
    `;
    let params: any[] = [];

    // Filter by gender if specified
    if (gender) {
      query += ' AND gender = ?';
      params.push(gender);
    }

    query += ' ORDER BY name ASC';

    console.log('Executing query:', query, 'with params:', params);
    const [rows] = await pool.execute(query, params);
    console.log(`Found ${(rows as any[]).length} sheep matching criteria`);

    // Calculate statistics
    const sheep = rows as any[];
    const stats = {
      total: sheep.length,
      healthy: sheep.filter(s => s.status === 'Healthy').length,
      sick: sheep.filter(s => s.status === 'Sick').length,
      injured: sheep.filter(s => s.status === 'Injured').length,
      quarantined: sheep.filter(s => s.status === 'Quarantined').length,
      pregnant: sheep.filter(s => s.status === 'Pregnant').length,
      lactating: sheep.filter(s => s.status === 'Lactating').length,
      males: sheep.filter(s => s.gender === 'Male').length,
      females: sheep.filter(s => s.gender === 'Female').length
    };

    console.log('Calculated stats:', stats);

    // Return both sheep and stats
    return NextResponse.json({
      sheep: sheep,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching sheep:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sheep' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    const {
      name,
      tagNumber,
      breed,
      gender,
      birthDate,
      acquisitionDate,
      status = 'Healthy',
      weight,
      color,
      markings,
      sire,
      dam,
      purchasePrice,
      isRegistered,
      registrationNumber,
      notes,
    } = body;

    const [result] = await pool.execute(
      `INSERT INTO animals (
        tag_number,
        name,
        animal_type,
        breed,
        gender,
        birth_date,
        acquisition_date,
        status,
        weight,
        color,
        markings,
        sire,
        dam,
        purchase_price,
        is_registered,
        registration_number,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tagNumber,
        name,
        'Sheep',
        breed,
        gender,
        birthDate || null,
        acquisitionDate || null,
        status,
        weight || null,
        color || null,
        markings || null,
        sire || null,
        dam || null,
        purchasePrice || null,
        isRegistered ? 1 : 0,
        registrationNumber || null,
        notes || null,
      ]
    );

    return NextResponse.json({
      message: 'Sheep added successfully',
      sheepId: (result as any).insertId
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error adding sheep:', error);
    
    // Handle duplicate tag number error
    if (error.code === 'ER_DUP_ENTRY') {
      return NextResponse.json(
        { error: 'Tag number already exists. Please use a unique tag number.' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to add sheep' },
      { status: 500 }
    );
  }
}
